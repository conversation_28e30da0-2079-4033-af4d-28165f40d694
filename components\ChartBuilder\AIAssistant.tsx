'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, Send, X, Co<PERSON>, Check } from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'

interface AIAssistantProps {
  selectedDatasets: Dataset[]
  language: 'sql' | 'python' | 'javascript' | 'markdown'
  onCodeGenerated: (code: string) => void
}

export function AIAssistant({ selectedDatasets, language, onCodeGenerated }: AIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [generatedCode, setGeneratedCode] = useState('')
  const [copied, setCopied] = useState(false)

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt')
      return
    }

    if (selectedDatasets.length === 0) {
      toast.error('Please select at least one dataset first')
      return
    }

    setIsLoading(true)
    try {
      // Prepare dataset information for the AI
      const datasetsInfo = selectedDatasets.map(ds => ({
        name: ds.name,
        columns: ds.headers,
        sampleData: ds.data.slice(0, 2), // First 2 rows as sample
        rowCount: ds.data.length
      }))

      const response = await fetch('/api/ai/chartbuilder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          language: language,
          datasets: datasetsInfo
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate code')
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to generate code')
      }

      setGeneratedCode(data.code || '')
      toast.success('Code generated successfully!')
    } catch (error) {
      console.error('Error generating code:', error)
      toast.error('Failed to generate code. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success('Code copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy code')
    }
  }

  const handleUseCode = () => {
    onCodeGenerated(generatedCode)
    setIsOpen(false)
    setPrompt('')
    setGeneratedCode('')
    toast.success('Code inserted into cell!')
  }

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        size="sm"
        variant="outline"
        className="gap-2"
      >
        <Sparkles className="h-4 w-4" />
        AI Assistant
      </Button>
    )
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            AI Code Assistant
          </CardTitle>
          <Button
            onClick={() => setIsOpen(false)}
            size="sm"
            variant="ghost"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2 mt-2">
          <Badge variant="secondary">{language.toUpperCase()}</Badge>
          {selectedDatasets.map((ds, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {ds.name}
            </Badge>
          ))}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            What would you like to do with your data?
          </label>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder={`Example: "Show the top 10 ${selectedDatasets[0]?.name || 'records'} by value" or "Create a chart showing trends over time"`}
            className="min-h-[80px]"
          />
        </div>
        
        <Button
          onClick={handleGenerate}
          disabled={isLoading || !prompt.trim()}
          className="w-full gap-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Generating...
            </>
          ) : (
            <>
              <Send className="h-4 w-4" />
              Generate Code
            </>
          )}
        </Button>

        {generatedCode && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Generated Code:</label>
              <div className="flex gap-2">
                <Button
                  onClick={handleCopy}
                  size="sm"
                  variant="outline"
                  className="gap-2"
                >
                  {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                  {copied ? 'Copied!' : 'Copy'}
                </Button>
                <Button
                  onClick={handleUseCode}
                  size="sm"
                  className="gap-2"
                >
                  Use Code
                </Button>
              </div>
            </div>
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md text-sm overflow-x-auto">
              <code>{generatedCode}</code>
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
