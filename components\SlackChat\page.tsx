"use client";

import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { createChannel, sendMessage, addReaction, getChannels, getMessages, generateInviteLink, getChannelMembers, deleteMessage } from '@/actions/chatActions'
import { getEmployees, getEmployeeDetails } from '@/actions/actions'
import { getNotes, getNote } from '@/actions/actions'
import { getSavedDiagrams, getPublicDiagram } from '@/app/actions/diagram'
import { Code, Bold, Italic, Copy, Users, NotepadText, Table as TableIcon, Link2, FileText, Search, BarChart, User, MessageSquare, ChevronDown, Plus, Hash, Smile, Send, Trash2, Menu, ArrowUpRight, Paperclip } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from "@clerk/nextjs";
import { cn } from "@/lib/utils";
import { ConnectionStatus } from './ConnectionStatus';
import { Message } from './Message';
import { pusherClient } from '@/lib/pusher';
import { TypingIndicator } from './TypingIndicator';
import { FileUpload } from './FileUpload';
import { ChatMessage } from '@/types/index';

// UI Components
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ScrollArea } from '../ui/scroll-area';
import { Input } from '../ui/input';

// Types
interface Reaction {
  emoji: string;
  count: number;
  users: string[];
}

interface Channel {
  id: string;
  name: string;
  unreadCount: number;
}

interface ChannelMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  email: string;
  poste: string;
}

interface Note {
  id: string;
  title: string;
  content: string;
  coverImage: string | null;
}

interface Diagram {
  id: string;
  title: string;
  content: {
    nodes: any[];
    edges: any[];
  };
}

interface ChatEvent {
  type: 'message' | 'messageDeleted' | 'typing';
  channelId: string;
  data: any;
}

// Constants
const emojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];

interface EmployeeTableProps {
  employee: Employee;
}

const EmployeeTableComponent = ({ employee }: EmployeeTableProps) => {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead colSpan={2}>Employee Information</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className="font-medium">Name</TableCell>
            <TableCell>{employee.prenom} {employee.nom}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">Position</TableCell>
            <TableCell>{employee.poste}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">Email</TableCell>
            <TableCell>{employee.email}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

interface MessageContentProps {
  content: string;
  type?: 'employee' | 'note' | 'diagram' | 'text';
  data?: any;
}

const MessageContent = ({ content, type, data }: MessageContentProps) => {
  if (type === 'employee' && data) {
    return <EmployeeTableComponent employee={data} />;
  }
  return <div>{content}</div>;
};

export default function EnhancedSlackClone() {
  // Router and Auth
  const router = useRouter();
  const params = useParams();
  const channelId = params.id as string;
  const { user: currentUser } = useUser();

  // Refs
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Chat State
  const [channels, setChannels] = useState<Channel[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [activeChannel, setActiveChannel] = useState<Channel | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [channelMembers, setChannelMembers] = useState<ChannelMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  // Slash Command State
  const [showCommandMenu, setShowCommandMenu] = useState(false);
  const [commandFilter, setCommandFilter] = useState('');
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const [diagrams, setDiagrams] = useState<Diagram[]>([]);
  const [showEmployeeList, setShowEmployeeList] = useState(false);
  const [showNoteList, setShowNoteList] = useState(false);
  const [showDiagramList, setShowDiagramList] = useState(false);

  // Add connection state
  const [isConnected, setIsConnected] = useState(false);

  // Add typing states
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Add Pusher connection
  useEffect(() => {
    if (!channelId) return;

    // Subscribe to Pusher channel
    const channel = pusherClient.subscribe(`chat-${channelId}`);

    // Handle new messages
    channel.bind('message', (data: ChatEvent) => {
          setMessages(prev => {
        if (prev.some(m => m.id === data.data.id)) return prev;
        return [...prev, data.data];
          });
          scrollRef.current?.scrollIntoView({ behavior: 'smooth' });
    });

    // Handle deleted messages
    channel.bind('messageDeleted', (data: ChatEvent) => {
      setMessages(prev => prev.filter(m => m.id !== data.data.messageId));
    });

    // Handle presence
    channel.bind('presence', (data: any) => {
      setChannelMembers(data.members);
    });

    // Add typing indicator binding
    channel.bind('typing', (data: { user: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        if (data.isTyping) {
          newSet.add(data.user);
        } else {
          newSet.delete(data.user);
        }
        return newSet;
      });
    });

    setIsConnected(true);

    return () => {
      pusherClient.unsubscribe(`chat-${channelId}`);
      setIsConnected(false);
    };
  }, [channelId]);

  // Fetch Data Functions
  const fetchData = async <T,>(
    fetchFn: () => Promise<{ success: boolean; data?: T; error?: string }>,
    errorMessage: string
  ): Promise<T | null> => {
    try {
      const result = await fetchFn();
      if (result.success && result.data) {
        return result.data;
      }
      toast.error(errorMessage);
      return null;
    } catch (error) {
      console.error(`Error: ${errorMessage}`, error);
      toast.error(`An error occurred while ${errorMessage.toLowerCase()}`);
      return null;
    }
  };

  // Slash Commands
  const slashCommands = [
    {
      id: 'note',
      name: 'Note',
      description: 'Insert a note',
      icon: <FileText className="h-4 w-4" />,
      action: async () => {
        try {
          const result = await getNotes();
          if (!result.success) {
            toast.error(result.error || "Failed to fetch notes");
            return;
          }
          // @ts-ignore
          setNotes(result.notes || []);
          setShowNoteList(true);
          setShowEmployeeList(false);
          setShowDiagramList(false);
        } catch (error) {
          console.error('Error fetching notes:', error);
          toast.error("An error occurred while fetching notes");
          setNotes([]);
        }
      }
    },
    {
      id: 'diagram',
      name: 'Diagram',
      description: 'Insert a diagram',
      icon: <BarChart className="h-4 w-4" />,
      action: async () => {
        try {
          const result = await getSavedDiagrams();
          if (!result.success) {
            toast.error(result.error || "Failed to fetch diagrams");
            return;
          }
          // @ts-ignore
          setDiagrams(result.data || []);
          setShowDiagramList(true);
          setShowEmployeeList(false);
          setShowNoteList(false);
        } catch (error) {
          console.error('Error fetching diagrams:', error);
          toast.error("An error occurred while fetching diagrams");
          setDiagrams([]);
        }
      }
    },
    {
      id: 'employee',
      name: 'Employee',
      description: 'Insert employee table',
      icon: <TableIcon className="h-4 w-4" />,
      action: async () => {
        try {
          const employees = await getEmployees();
          if (!employees) {
            toast.error("No employees found");
            return;
          }
          setEmployees(employees);
          setShowEmployeeList(true);
          setShowNoteList(false);
          setShowDiagramList(false);
        } catch (error) {
          console.error('Error fetching employees:', error);
          toast.error("An error occurred while fetching employees");
          setEmployees([]);
        }
      }
    }
  ];

  // Insert Functions
  const insertEmployee = (employee: Employee) => {
    const messageContent = {
      type: 'employee',
      data: employee,
      text: 'Employee Information'
    };
    setNewMessage(prev => prev.replace(/\/\w*$/, '') + JSON.stringify(messageContent));
    setShowEmployeeList(false);
  };

  const insertNote = (note: Note) => {
    const noteUrl = `localhost:3000/p/${note.id}`;
    const noteMarkdown = `[📝 ${note.title}](${noteUrl})`;
    setNewMessage(prev => prev.replace(/\/\w*$/, '') + noteMarkdown);
    setShowNoteList(false);
  };

  const insertDiagram = (diagram: Diagram) => {
    const diagramUrl = `localhost:3000/p/${diagram.id}`;
    const diagramMarkdown = `[📊 ${diagram.title}](${diagramUrl})`;
    setNewMessage(prev => prev.replace(/\/\w*$/, '') + diagramMarkdown);
    setShowDiagramList(false);
  };

  const handleSendMessage = async (e: React.FormEvent, fileUrl?: string) => {
    e.preventDefault();
    if (!activeChannel || !currentUser || (!newMessage.trim() && !fileUrl)) return;

    const promise = new Promise(async (resolve, reject) => {
      try {
        const messageData = {
          channelId: activeChannel.id,
          content: newMessage.trim(),
          userId: currentUser.id,
          fileKey: fileUrl ? fileUrl.split('/').pop() : undefined,
          fileType: fileUrl ? getFileType(fileUrl) : undefined,
        };

        const result = await sendMessage(messageData);
        if (result.success) {
          setNewMessage('');
          setShowFileUpload(false);
          resolve(result);
        } else {
          reject(result.error || "Failed to send message");
        }
      } catch (error) {
        reject("An error occurred while sending the message");
      }
    });

    toast.promise(promise, {
      loading: 'Sending message...',
      success: 'Message sent!',
      error: (err) => `${err}`
    });
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    const result = await addReaction(messageId, emoji)
    if (result.success) {
      const channel = channels.find(c => c.id === activeChannel?.id);
      if (channel) {
        fetchMessages(channel.id);
      }
    } else {
      // Handle error
      console.error(result.error)
    }
  }

  const handleChannelClick = (channel: Channel) => {
    router.push(`/hr/workspace/chats/${channel.id}`)
  }

  const handleReply = (messageId: string) => {
    const messageToReply = messages.find(m => m.id === messageId);
    if (messageToReply) {
    setReplyingTo(messageId);
      // Show the reply context in the input
      setNewMessage(`@${messageToReply.user} `);
      // Focus the input field
      inputRef.current?.focus();
    }
  };

  const filteredMessages = messages.filter(msg => 
    msg.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    msg.user.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const userColors = {
    owner: 'bg-primary text-primary-foreground',
    member: 'bg-muted text-foreground',
    response: 'bg-muted/80 text-foreground',
  };

  const getUserColor = (userId: string) => {
    const colors = Object.values(userColors);
    const colorIndex = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    return colors[colorIndex];
  };

  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - messageDate.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else if (diffInSeconds < 2592000) {
      const weeks = Math.floor(diffInSeconds / 604800);
      return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else {
      return messageDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!activeChannel?.id) return;
    
    const promise = deleteMessage(messageId, activeChannel.id);
    
    toast.promise(promise, {
      loading: 'Deleting message...',
      success: () => 'Message deleted!',
      error: 'Failed to delete message'
    });
  };

  const handleTyping = () => {
    if (!currentUser || !activeChannel) return;

    if (!isTyping) {
      setIsTyping(true);
      // Use fetch to trigger the typing event
      fetch('/api/slackchat/subscribe', {
        method: 'POST',
        body: JSON.stringify({
          type: 'typing',
          channelId: activeChannel.id,
          data: {
            user: currentUser.fullName || currentUser.username,
            isTyping: true
          }
        })
      });
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      fetch('/api/slackchat/subscribe', {
        method: 'POST',
        body: JSON.stringify({
          type: 'typing',
          channelId: activeChannel.id,
          data: {
            user: currentUser.fullName || currentUser.username,
            isTyping: false
          }
        })
      });
    }, 2000);
  };

  const handleGenerateInviteLink = async () => {
    if (!activeChannel) return;
    
    toast.promise(
      generateInviteLink(activeChannel.id),
      {
        loading: 'Generating invite link...',
        success: (result) => {
          if (result.success) {
            const fullUrl = `${window.location.origin}${result.inviteLink}`;
            navigator.clipboard.writeText(fullUrl);
            return 'Invite link copied to clipboard!';
          }
          throw new Error(result.error || 'Failed to generate invite link');
        },
        error: 'Failed to generate invite link'
      }
    );
  };

  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSend = async (file: File) => {
    if (!activeChannel || !currentUser) return;

    setIsUploading(true);
    try {
      // Convert file to base64
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = async () => {
        const base64File = reader.result as string;
        
        // Send file through Pusher
        await fetch('/api/slackchat/subscribe', {
          method: 'POST',
          body: JSON.stringify({
            type: 'message',
            channelId: activeChannel.id,
            data: {
              id: Date.now().toString(),
              user: currentUser.fullName || currentUser.username,
              userId: currentUser.id,
              content: '',
              timestamp: new Date().toISOString(),
              fileData: {
                name: file.name,
                type: file.type,
                size: file.size,
                data: base64File
              }
            }
          })
        });

        setShowFileUpload(false);
        toast.success('File sent successfully!');
      };
    } catch (error) {
      console.error('Error sending file:', error);
      toast.error('Failed to send file');
    } finally {
      setIsUploading(false);
    }
  };



  useEffect(() => {
    fetchChannels();
  }, []);

  useEffect(() => {
    if (channelId) {
      fetchMessages(channelId);
      fetchChannelMembers(channelId);
      const channel = channels.find(c => c.id === channelId);
      if (channel) {
        setActiveChannel(channel);
      }
    }
  }, [channelId, channels]);

  const fetchChannels = async () => {
    try {
      const result = await getChannels();
      if (result.success) {
        setChannels(result.channels.map(channel => ({
          id: channel.id,
          name: channel.name,
          unreadCount: 0 // You can implement this later
        })));
        
        if (result.channels.length > 0 && !channelId) {
          router.push(`/hr/workspace/chats/${result.channels[0].id}`);
        }
      } else {
        toast.error("Failed to fetch channels");
      }
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast.error("An error occurred while fetching channels");
    }
  };

  const fetchMessages = async (id: string) => {
    const result = await getMessages(id)
    if (result.success) {
      // @ts-ignore
      setMessages(result.messages)
    } else {
      // Handle error
      console.error(result.error)
    }
  }

  const fetchChannelMembers = async (channelId: string) => {
    try {
      const result = await getChannelMembers(channelId);
      if (result.success && result.members) {
        // Create a mock online status based on random logic or last activity
        setChannelMembers(result.members.map(member => ({
          id: member.id,
          name: member.name,
          avatar: undefined,
          isOnline: Math.random() > 0.5, // Mock online status
          lastSeen: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) // Mock last seen
        })));
      } else {
        toast.error(result.error || "Failed to fetch channel members");
      }
    } catch (error) {
      console.error('Error fetching channel members:', error);
      toast.error("An error occurred while fetching channel members");
    }
  };

  const renderMessages = () => {
    if (!messages.length) {
      return (
        <div className="flex-1 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No messages yet</p>
            <p className="text-sm">Be the first to send a message!</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 overflow-y-auto">
        {messages.map((message: ChatMessage) => (
          <Message
            key={message.id}
            message={message}
            channelId={channelId}
            isCurrentUser={message.userId === currentUser?.id}
            userColors={userColors}
            onDelete={handleDeleteMessage}
            onReply={handleReply}
            onReaction={handleReaction}
            replyTo={message.replyTo}
            readBy={message.readBy}
            fileUrl={message.fileUrl}
            fileType={message.fileType}
          />
        ))}
        
        {/* Typing Indicator */}
        {typingUsers.size > 0 && (
          <TypingIndicator 
            userName={Array.from(typingUsers).join(', ')} 
          />
        )}
        
        {/* Scroll Anchor */}
        <div ref={scrollRef} />
      </div>
    );
  };

  const renderReplyingTo = () => {
    if (!replyingTo) return null;
    
    const messageToReply = messages.find(m => m.id === replyingTo);
    if (!messageToReply) return null;

    return (
      <div className="px-4 py-2 border-t flex items-center justify-between bg-muted/50">
        <div className="flex items-center gap-2 text-sm">
          <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground">Replying to</span>
          <span className="font-medium text-blue-500">{messageToReply.user}</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setReplyingTo(null);
            setNewMessage('');
          }}
        >
          Cancel
        </Button>
      </div>
    );
  };

  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) return 'image';
    if (extension === 'pdf') return 'pdf';
    if (extension === 'csv') return 'csv';
    return 'other';
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="border-b px-4 py-2 flex items-center justify-between bg-background">
        <h1 className="text-sm font-semibold">HR Chat</h1>
        <ConnectionStatus isConnected={isConnected} />
                        </div>

      <ResizablePanelGroup direction="horizontal">
        <ResizablePanel defaultSize={20} minSize={15} maxSize={25}>
          <div className="h-full flex flex-col border-r">
            {/* Channels Section */}
            <div className="p-2 border-b">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-sm font-medium">Channels</h2>
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <Plus className="h-4 w-4" />
                </Button>
                        </div>
              <ScrollArea className="h-[200px]">
                <div className="space-y-1">
                  {channels.map(channel => (
                              <button
                      key={channel.id}
                      onClick={() => handleChannelClick(channel)}
                      className={cn(
                        "w-full flex items-center gap-2 px-2 py-1.5 rounded-md text-sm",
                        activeChannel?.id === channel.id 
                          ? "bg-primary/10 text-primary font-medium" 
                          : "hover:bg-muted"
                      )}
                    >
                      <Hash className="h-3.5 w-3.5" />
                      <span className="truncate">{channel.name}</span>
                              </button>
                            ))}
                        </div>
              </ScrollArea>
                      </div>

            {/* Connected Users Section */}
            <div className="p-2 flex-1">
              <h2 className="text-sm font-medium mb-2">Connected Users</h2>
              <ScrollArea className="h-[calc(100vh-350px)]">
                <div className="space-y-1">
                  {channelMembers.map(member => (
                    <div 
                      key={member.id}
                      className="flex items-center gap-2 px-2 py-1.5 hover:bg-muted/50 rounded-md transition-colors"
                    >
                      <div className="relative">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback className="text-[10px]">
                          {member.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                        <span className={cn(
                          "absolute -bottom-0.5 -right-0.5 flex h-2.5 w-2.5",
                          member.isOnline ? "text-green-500" : "text-muted"
                        )}>
                          {member.isOnline && (
                            <span className="animate-ping absolute inline-flex h-full w-full rounded-full opacity-75 bg-current" />
                          )}
                          <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-current" />
                        </span>
                      </div>
                      <div className="flex flex-col min-w-0">
                        <span className="text-sm font-medium leading-none truncate">
                          {member.name}
                        </span>
                        {!member.isOnline && member.lastSeen && (
                          <span className="text-[10px] text-muted-foreground truncate">
                            last seen {getRelativeTime(member.lastSeen.toISOString())}
                          </span>
                        )}
                      </div>
                    </div>
                            ))}
                        </div>
              </ScrollArea>
                      </div>
                      </div>
        </ResizablePanel>

        <ResizableHandle withHandle />

        <ResizablePanel defaultSize={80}>
          <div className="flex flex-col h-full">
            {/* Channel Header */}
            <div className="border-b bg-background px-4 py-2 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <h2 className="text-sm font-medium">{activeChannel?.name}</h2>
              </div>
                            <Button
                              variant="ghost"
                              size="sm"
                className="text-xs"
                onClick={handleGenerateInviteLink}
                            >
                <Copy className="h-3.5 w-3.5 mr-1.5" />
                Share
                            </Button>
                        </div>

            {/* Messages Area */}
            <ScrollArea className="flex-1 px-4">
              {renderMessages()}
            </ScrollArea>

            {/* Input Area */}
            <div className="border-t bg-background">
              {renderReplyingTo()}
              {showFileUpload ? (
                <FileUpload
                  // @ts-ignore 
                  onFileSelect={handleFileSend}
                  onCancel={() => setShowFileUpload(false)}
                  isUploading={isUploading}
                  onMessageSubmit={handleSendMessage}
                  message={newMessage}
                  onMessageChange={(e) => setNewMessage(e.target.value)}
                  placeholder={`Message ${activeChannel?.name ? `#${activeChannel.name}` : 'channel'}`}
                />
              ) : (
                <form onSubmit={handleSendMessage} className="flex items-center gap-2 p-4">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0"
                    onClick={() => setShowFileUpload(true)}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                <Input
                  ref={inputRef}
                  type="text"
                  placeholder={`Message ${activeChannel?.name ? `#${activeChannel.name}` : 'channel'}`}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" size="sm">
                  <Send className="h-4 w-4" />
                    </Button>
                </form>
              )}
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}