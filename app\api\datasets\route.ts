import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";

// Add this type for better type safety
type ChangeHistory = {
  timestamp: Date;
  userId: string;
  userName: string;
  changes: {
    row: number;
    column: string;
    oldValue: any;
    newValue: any;
  }[];
};

export async function GET(req: Request) {
  try {
    const { userId } = auth();
    const { searchParams } = new URL(req.url);
    const datasetId = searchParams.get('datasetId');

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    // Try to find the user, and if not found, try to create them
    let user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      console.log(`User with clerkId ${userId} not found in database. Attempting to create...`);

      // Try to get user info from <PERSON> and create the user
      try {
        const { currentUser } = await import("@clerk/nextjs/server");
        const clerkUser = await currentUser();

        if (clerkUser) {
          user = await prisma.user.create({
            data: {
              clerkId: userId,
              email: clerkUser.primaryEmailAddress?.emailAddress || `${userId}@unknown.com`,
              name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'Unknown User'
            },
            select: { id: true }
          });
          console.log(`Successfully created user with clerkId ${userId}`);
        } else {
          return NextResponse.json(
            { error: "User not found in Clerk" },
            { status: 404 }
          );
        }
      } catch (createError) {
        console.error('Error creating user:', createError);
        return NextResponse.json(
          { error: "Failed to create user in database" },
          { status: 500 }
        );
      }
    }

    // If datasetId is provided, return specific dataset with version history
    if (datasetId) {
      const dataset = await prisma.dataSet.findUnique({
        where: { id: datasetId },
        include: {
          changes: {
            orderBy: {
              version: 'desc'
            }
          }
        }
      });

      if (!dataset) {
        return NextResponse.json(
          { error: "Dataset not found" },
          { status: 404 }
        );
      }

      // Format version history
      const versions = dataset.changes.map(change => ({
        id: change.id,
        versionNumber: change.version,
        changes: change.changes,
        createdAt: change.timestamp,
        user: {
          name: change.userName
        }
      }));

      // Calculate dataset size
      const dataSize = new TextEncoder().encode(JSON.stringify(dataset.data)).length;
      const formattedSize = dataSize < 1024 * 1024
        ? `${(dataSize / 1024).toFixed(2)} KB`
        : `${(dataSize / (1024 * 1024)).toFixed(2)} MB`;

      return NextResponse.json({
        success: true,
        versions,
        datasetInfo: {
          id: dataset.id,
          name: dataset.name,
          description: dataset.description,
          lastModified: dataset.updatedAt,
          rowCount: Array.isArray(dataset.data) ? dataset.data.length : 0,
          columnCount: dataset.headers.length,
          size: formattedSize,
          fileType: dataset.fileType,
          data: dataset.data, // Make sure to include the actual data
          headers: dataset.headers
        }
      });
    }

    // If no datasetId, return all datasets
    const datasets = await prisma.dataSet.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        description: true,
        fileType: true,
        createdAt: true,
        headers: true,
        data: true,
        folderId: true, // Include folderId for folder organization
        embedding: true, // Include embedding status
        embeddingModel: true // Include embedding model
      }
    });

    console.log(`Fetched ${datasets.length} datasets for user ${user.id}`);
    if (datasets.length > 0 && datasets[0] && datasets[0].data) {
      console.log('Sample dataset:', {
        id: datasets[0].id,
        name: datasets[0].name,
        folderId: datasets[0].folderId || null,
        dataLength: Array.isArray(datasets[0].data) ? datasets[0].data.length : 0
      });
    }

    return NextResponse.json({
      success: true,
      datasets
    });
  } catch (error) {
    console.error('Error fetching data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch data' },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { name, description, data, headers, fileType } = await req.json();

    // Create new dataset with actual data
    const dataset = await prisma.dataSet.create({
      data: {
        name: name.trim(),
        description: description?.trim(),
        data: data,
        headers,
        fileType,
        userId: user.id,
      }
    });

    return NextResponse.json({
      success: true,
      dataset: {
        id: dataset.id,
        name: dataset.name,
        description: dataset.description,
        fileType: dataset.fileType,
        createdAt: dataset.createdAt,
        headers: dataset.headers,
        data: dataset.data,
        rowCount: data.length
      }
    });
  } catch (error) {
    console.error('Error saving dataset:', error);
    return NextResponse.json(
      { error: 'Failed to save dataset' },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: {
        id: true,
        name: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { datasetId, changes, newData } = await req.json();

    if (!datasetId) {
      return NextResponse.json(
        { error: "Dataset ID is required" },
        { status: 400 }
      );
    }

    // Get the existing dataset and its latest version number
    const existingDataset = await prisma.dataSet.findUnique({
      where: { id: datasetId },
      include: {
        changes: {
          orderBy: {
            version: 'desc'
          },
          take: 1
        }
      }
    });

    if (!existingDataset) {
      return NextResponse.json(
        { error: "Dataset not found" },
        { status: 404 }
      );
    }

    // Calculate next version number
    const nextVersion = existingDataset.changes.length > 0
      ? existingDataset.changes[0].version + 1
      : 1;

    // Update dataset with new data
    const updatedDataset = await prisma.dataSet.update({
      where: { id: datasetId },
      data: {
        data: newData,
        updatedAt: new Date()
      }
    });

    // Store the changes
    await prisma.dataSetChange.create({
      data: {
        dataSetId: datasetId,
        userId: user.id,
        userName: user.name || 'Unknown User',
        changes: changes,
        version: nextVersion
      }
    });

    return NextResponse.json({
      success: true,
      version: {
        id: updatedDataset.id,
        timestamp: new Date(),
        changes,
        userName: user.name || 'Unknown User'
      }
    });
  } catch (error) {
    console.error('Error saving changes:', error);
    return NextResponse.json(
      { error: 'Failed to save changes' },
      { status: 500 }
    );
  }
}