'use client'

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calculator, AlertCircle, Copy, Trash2, PlusCircle, Save, TrendingUp, DollarSign, Users, Target, BarChart3, PieChart, Activity, Zap, Star, Award, Briefcase, Calendar, FileText, Settings, Heart, Shield, Globe, Home, Hash } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory, FormulaFunction, CellReference } from './types'
import { useChartSaving } from '../chartbuilderlogic/useChartSaving'

// Available icons for calculator cards
const AVAILABLE_ICONS = [
  { name: 'Calculator', icon: Calculator, color: 'text-blue-600' },
  { name: 'TrendingUp', icon: TrendingUp, color: 'text-green-600' },
  { name: 'DollarSign', icon: DollarSign, color: 'text-emerald-600' },
  { name: 'Users', icon: Users, color: 'text-purple-600' },
  { name: 'Target', icon: Target, color: 'text-red-600' },
  { name: 'BarChart3', icon: BarChart3, color: 'text-indigo-600' },
  { name: 'PieChart', icon: PieChart, color: 'text-pink-600' },
  { name: 'Activity', icon: Activity, color: 'text-orange-600' },
  { name: 'Zap', icon: Zap, color: 'text-yellow-600' },
  { name: 'Star', icon: Star, color: 'text-amber-600' },
  { name: 'Award', icon: Award, color: 'text-cyan-600' },
  { name: 'Briefcase', icon: Briefcase, color: 'text-slate-600' },
  { name: 'Calendar', icon: Calendar, color: 'text-teal-600' },
  { name: 'FileText', icon: FileText, color: 'text-gray-600' },
  { name: 'Settings', icon: Settings, color: 'text-stone-600' },
  { name: 'Heart', icon: Heart, color: 'text-rose-600' },
  { name: 'Shield', icon: Shield, color: 'text-blue-700' },
  { name: 'Globe', icon: Globe, color: 'text-green-700' },
  { name: 'Home', icon: Home, color: 'text-blue-500' },
  { name: 'Hash', icon: Hash, color: 'text-gray-500' }
]

interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
  onCellClick?: (cellRef: string) => void
  onColumnClick?: (columnRef: string) => void
}

// Comprehensive Excel-like functions
const FORMULA_FUNCTIONS: FormulaFunction[] = [
  // Mathematical Functions
  {
    name: 'SUM',
    description: 'Adds all numbers in a range',
    syntax: 'SUM(column_name)',
    example: 'SUM(salary)',
    category: 'math'
  },
  {
    name: 'PRODUCT',
    description: 'Multiplies all numbers in a range',
    syntax: 'PRODUCT(column_name)',
    example: 'PRODUCT(quantity)',
    category: 'math'
  },
  {
    name: 'POWER',
    description: 'Returns a number raised to a power',
    syntax: 'POWER(number, power)',
    example: 'POWER(2, 3)',
    category: 'math'
  },
  {
    name: 'SQRT',
    description: 'Returns the square root of a number',
    syntax: 'SQRT(number)',
    example: 'SQRT(16)',
    category: 'math'
  },
  {
    name: 'ABS',
    description: 'Returns the absolute value of a number',
    syntax: 'ABS(number)',
    example: 'ABS(-5)',
    category: 'math'
  },
  {
    name: 'ROUND',
    description: 'Rounds a number to specified decimals',
    syntax: 'ROUND(number, decimals)',
    example: 'ROUND(3.14159, 2)',
    category: 'math'
  },
  {
    name: 'CEILING',
    description: 'Rounds a number up to the nearest integer',
    syntax: 'CEILING(number)',
    example: 'CEILING(4.2)',
    category: 'math'
  },
  {
    name: 'FLOOR',
    description: 'Rounds a number down to the nearest integer',
    syntax: 'FLOOR(number)',
    example: 'FLOOR(4.8)',
    category: 'math'
  },
  {
    name: 'MOD',
    description: 'Returns the remainder after division',
    syntax: 'MOD(number, divisor)',
    example: 'MOD(10, 3)',
    category: 'math'
  },

  // Statistical Functions
  {
    name: 'AVERAGE',
    description: 'Calculates the average of numbers',
    syntax: 'AVERAGE(column_name)',
    example: 'AVERAGE(age)',
    category: 'statistical'
  },
  {
    name: 'MEDIAN',
    description: 'Returns the median of numbers',
    syntax: 'MEDIAN(column_name)',
    example: 'MEDIAN(salary)',
    category: 'statistical'
  },
  {
    name: 'MODE',
    description: 'Returns the most frequently occurring value',
    syntax: 'MODE(column_name)',
    example: 'MODE(department)',
    category: 'statistical'
  },
  {
    name: 'COUNT',
    description: 'Counts non-empty cells',
    syntax: 'COUNT(column_name)',
    example: 'COUNT(employee_id)',
    category: 'statistical'
  },
  {
    name: 'COUNTA',
    description: 'Counts non-empty cells including text',
    syntax: 'COUNTA(column_name)',
    example: 'COUNTA(name)',
    category: 'statistical'
  },
  {
    name: 'COUNTIF',
    description: 'Counts cells that meet criteria',
    syntax: 'COUNTIF(column_name, criteria)',
    example: 'COUNTIF(department, "IT")',
    category: 'statistical'
  },
  {
    name: 'MIN',
    description: 'Finds the minimum value',
    syntax: 'MIN(column_name)',
    example: 'MIN(salary)',
    category: 'statistical'
  },
  {
    name: 'MAX',
    description: 'Finds the maximum value',
    syntax: 'MAX(column_name)',
    example: 'MAX(salary)',
    category: 'statistical'
  },
  {
    name: 'STDEV',
    description: 'Calculates standard deviation',
    syntax: 'STDEV(column_name)',
    example: 'STDEV(salary)',
    category: 'statistical'
  },
  {
    name: 'VAR',
    description: 'Calculates variance',
    syntax: 'VAR(column_name)',
    example: 'VAR(age)',
    category: 'statistical'
  },

  // Logical Functions
  {
    name: 'IF',
    description: 'Returns one value if condition is true, another if false',
    syntax: 'IF(condition, true_value, false_value)',
    example: 'IF(AVERAGE(salary) > 50000, "High", "Low")',
    category: 'logical'
  },
  {
    name: 'AND',
    description: 'Returns true if all conditions are true',
    syntax: 'AND(condition1, condition2)',
    example: 'AND(MIN(age) >= 18, MAX(age) <= 65)',
    category: 'logical'
  },
  {
    name: 'OR',
    description: 'Returns true if any condition is true',
    syntax: 'OR(condition1, condition2)',
    example: 'OR(SUM(salary) > 100000, COUNT(employee_id) > 10)',
    category: 'logical'
  },
  {
    name: 'NOT',
    description: 'Reverses the logic of its argument',
    syntax: 'NOT(condition)',
    example: 'NOT(AVERAGE(age) < 30)',
    category: 'logical'
  },

  // Text Functions
  {
    name: 'CONCATENATE',
    description: 'Joins text strings together',
    syntax: 'CONCATENATE(text1, text2)',
    example: 'CONCATENATE("Total: ", SUM(salary))',
    category: 'text'
  },
  {
    name: 'LEN',
    description: 'Returns the length of text',
    syntax: 'LEN(text)',
    example: 'LEN("Hello World")',
    category: 'text'
  },
  {
    name: 'UPPER',
    description: 'Converts text to uppercase',
    syntax: 'UPPER(text)',
    example: 'UPPER("hello")',
    category: 'text'
  },
  {
    name: 'LOWER',
    description: 'Converts text to lowercase',
    syntax: 'LOWER(text)',
    example: 'LOWER("HELLO")',
    category: 'text'
  },

  // Date Functions
  {
    name: 'TODAY',
    description: 'Returns today\'s date',
    syntax: 'TODAY()',
    example: 'TODAY()',
    category: 'date'
  },
  {
    name: 'NOW',
    description: 'Returns current date and time',
    syntax: 'NOW()',
    example: 'NOW()',
    category: 'date'
  },
  {
    name: 'YEAR',
    description: 'Extracts year from date',
    syntax: 'YEAR(date)',
    example: 'YEAR(TODAY())',
    category: 'date'
  },
  {
    name: 'MONTH',
    description: 'Extracts month from date',
    syntax: 'MONTH(date)',
    example: 'MONTH(TODAY())',
    category: 'date'
  },
  {
    name: 'DAY',
    description: 'Extracts day from date',
    syntax: 'DAY(date)',
    example: 'DAY(TODAY())',
    category: 'date'
  },

  // Advanced Mathematical Functions
  {
    name: 'EXP',
    description: 'Returns e raised to the power of number',
    syntax: 'EXP(number)',
    example: 'EXP(1)',
    category: 'math'
  },
  {
    name: 'LN',
    description: 'Returns the natural logarithm',
    syntax: 'LN(number)',
    example: 'LN(10)',
    category: 'math'
  },
  {
    name: 'LOG10',
    description: 'Returns the base-10 logarithm',
    syntax: 'LOG10(number)',
    example: 'LOG10(100)',
    category: 'math'
  },
  {
    name: 'SIN',
    description: 'Returns the sine of an angle',
    syntax: 'SIN(angle)',
    example: 'SIN(PI()/2)',
    category: 'math'
  },
  {
    name: 'COS',
    description: 'Returns the cosine of an angle',
    syntax: 'COS(angle)',
    example: 'COS(0)',
    category: 'math'
  },
  {
    name: 'TAN',
    description: 'Returns the tangent of an angle',
    syntax: 'TAN(angle)',
    example: 'TAN(PI()/4)',
    category: 'math'
  },
  {
    name: 'PI',
    description: 'Returns the value of pi',
    syntax: 'PI()',
    example: 'PI()',
    category: 'math'
  },

  // Advanced Statistical Functions
  {
    name: 'MEDIAN',
    description: 'Returns the median of numbers',
    syntax: 'MEDIAN(range)',
    example: 'MEDIAN(A1:A10)',
    category: 'statistical'
  },
  {
    name: 'STDEV',
    description: 'Calculates standard deviation',
    syntax: 'STDEV(range)',
    example: 'STDEV(A1:A10)',
    category: 'statistical'
  },

  // Advanced Text Functions
  {
    name: 'LEFT',
    description: 'Returns leftmost characters',
    syntax: 'LEFT(text, num_chars)',
    example: 'LEFT("Hello", 2)',
    category: 'text'
  },
  {
    name: 'RIGHT',
    description: 'Returns rightmost characters',
    syntax: 'RIGHT(text, num_chars)',
    example: 'RIGHT("Hello", 2)',
    category: 'text'
  },
  {
    name: 'MID',
    description: 'Returns characters from middle of text',
    syntax: 'MID(text, start, length)',
    example: 'MID("Hello", 2, 3)',
    category: 'text'
  },
  {
    name: 'TRIM',
    description: 'Removes extra spaces',
    syntax: 'TRIM(text)',
    example: 'TRIM(" Hello ")',
    category: 'text'
  },

  // Advanced Logical Functions
  {
    name: 'ISNUMBER',
    description: 'Tests if value is a number',
    syntax: 'ISNUMBER(value)',
    example: 'ISNUMBER(A1)',
    category: 'logical'
  },
  {
    name: 'ISTEXT',
    description: 'Tests if value is text',
    syntax: 'ISTEXT(value)',
    example: 'ISTEXT(A1)',
    category: 'logical'
  },
  {
    name: 'ISBLANK',
    description: 'Tests if cell is blank',
    syntax: 'ISBLANK(cell)',
    example: 'ISBLANK(A1)',
    category: 'logical'
  },

  // Financial Functions
  {
    name: 'PMT',
    description: 'Calculates loan payment',
    syntax: 'PMT(rate, nper, pv)',
    example: 'PMT(0.05/12, 360, 100000)',
    category: 'math'
  },
  {
    name: 'FV',
    description: 'Calculates future value',
    syntax: 'FV(rate, nper, pmt, pv)',
    example: 'FV(0.05, 10, -1000, 0)',
    category: 'math'
  }
]

export function FormulaCalculator({ data, columns, onClose, onCellClick, onColumnClick }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [selectedFunction, setSelectedFunction] = useState<FormulaFunction | null>(null)
  const [cursorPosition, setCursorPosition] = useState(0)
  const [isCalculating, setIsCalculating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const formulaInputRef = useRef<HTMLInputElement>(null)

  // Save dialog state
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveTitle, setSaveTitle] = useState('')
  const [saveDescription, setSaveDescription] = useState('')
  const [selectedIcon, setSelectedIcon] = useState('Calculator')

  // Use the chart saving hook for dashboard functionality
  const { handleSaveCalculatorResult } = useChartSaving()

  // Helper function to convert number to Excel column name (0->A, 1->B, 25->Z, 26->AA)
  const getExcelColumnName = useCallback((index: number): string => {
    let result = ''
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result
      index = Math.floor(index / 26) - 1
    }
    return result
  }, [])

  // Helper function to convert Excel column name to index (A->0, B->1, AA->26)
  const getColumnIndex = useCallback((columnName: string): number => {
    let result = 0
    for (let i = 0; i < columnName.length; i++) {
      result = result * 26 + (columnName.charCodeAt(i) - 64)
    }
    return result - 1
  }, [])

  // Create Excel-like column mapping (A, B, C, etc.)
  const columnMapping = useMemo(() => {
    const mapping: Record<string, string> = {}
    const reverseMapping: Record<string, string> = {}
    const indexMapping: Record<number, string> = {}

    columns.forEach((column, index) => {
      const excelColumn = getExcelColumnName(index)
      mapping[excelColumn] = column
      reverseMapping[column] = excelColumn
      indexMapping[index] = column
    })

    console.log('Excel column mapping:', mapping)
    return { mapping, reverseMapping, indexMapping }
  }, [columns, getExcelColumnName])

  // Helper function to parse Excel cell reference (A1, B2, etc.)
  const parseCellReference = useCallback((cellRef: string): { column: string, row: number } | null => {
    console.log(`Parsing cell reference: ${cellRef}`)
    const match = cellRef.match(/^([A-Z]+)(\d+)$/)
    if (!match) {
      console.log(`Failed to match cell reference pattern: ${cellRef}`)
      return null
    }

    const columnName = match[1]
    const rowNumber = parseInt(match[2]) - 1 // Convert to 0-based index
    console.log(`Column name: ${columnName}, Row number: ${rowNumber}`)
    
    const actualColumn = columnMapping.mapping[columnName]
    console.log(`Mapped column name ${columnName} to actual column: ${actualColumn}`)

    if (!actualColumn) {
      console.log(`No mapping found for column ${columnName}`)
      return null
    }
    
    if (rowNumber < 0 || rowNumber >= data.length) {
      console.log(`Row index out of bounds: ${rowNumber}, data length: ${data.length}`)
      return null
    }

    console.log(`Successfully parsed ${cellRef} to column: ${actualColumn}, row: ${rowNumber}`)
    return { column: actualColumn, row: rowNumber }
  }, [columnMapping, data])

  // Get cell value by Excel reference (A1, B2, etc.)
  const getCellValue = useCallback((cellRef: string): any => {
    const parsed = parseCellReference(cellRef)
    if (!parsed) {
      console.log(`Cell reference ${cellRef} could not be parsed`)
      return 0
    }

    // Make sure we have data for this row
    if (!data[parsed.row]) {
      console.log(`No data for row ${parsed.row}`)
      return 0
    }

    const value = data[parsed.row][parsed.column]
    console.log(`Cell ${cellRef} -> row: ${parsed.row}, column: ${parsed.column}, value: ${value}`)
    
    // Handle different value types
    if (value === null || value === undefined) {
      return 0
    } else if (typeof value === 'number') {
      return value
    } else if (typeof value === 'string' && !isNaN(Number(value))) {
      return Number(value)
    } else if (typeof value === 'boolean') {
      return value ? 1 : 0
    } else {
      // For non-numeric strings or other types
      return 0
    }
  }, [parseCellReference, data])

  // Helper function to evaluate Excel functions with proper range handling
  const evaluateExcelFunction = useCallback((functionName: string, args: string): string => {
    console.log(`Evaluating ${functionName} with args: ${args}`)

    // Handle range references (like A1:A4, AP1:AP4)
    const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
    if (rangeMatch) {
      const [_, startCol, startRow, endCol, endRow] = rangeMatch
      console.log(`Range detected: ${startCol}${startRow}:${endCol}${endRow}`)

      const startColIndex = getColumnIndex(startCol)
      const endColIndex = getColumnIndex(endCol)
      const startRowIndex = parseInt(startRow) - 1
      const endRowIndex = parseInt(endRow) - 1

      console.log(`Column indices: ${startColIndex} to ${endColIndex}`)
      console.log(`Row indices: ${startRowIndex} to ${endRowIndex}`)

      // Handle reversed ranges correctly
      const minColIndex = Math.min(startColIndex, endColIndex)
      const maxColIndex = Math.max(startColIndex, endColIndex)
      const minRowIndex = Math.min(startRowIndex, endRowIndex)
      const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)

      const values: number[] = []

      for (let row = minRowIndex; row <= maxRowIndex; row++) {
        if (row < 0) continue;

        for (let col = minColIndex; col <= maxColIndex; col++) {
          if (col < 0 || col >= columns.length) continue;

          const columnName = getExcelColumnName(col)
          const cellRef = `${columnName}${row + 1}`
          const value = getCellValue(cellRef)

          if (typeof value === 'number') {
            values.push(value)
          } else if (typeof value === 'string' && !isNaN(Number(value))) {
            values.push(Number(value))
          }
        }
      }

      console.log(`Extracted values from range: [${values.join(',')}]`)

      // Apply the function
      switch (functionName.toUpperCase()) {
        case 'SUM':
          const sum = values.reduce((acc, val) => acc + val, 0)
          console.log(`SUM result: ${sum}`)
          return sum.toString()
        case 'AVERAGE':
          const avg = values.length > 0 ? values.reduce((acc, val) => acc + val, 0) / values.length : 0
          console.log(`AVERAGE result: ${avg}`)
          return avg.toString()
        case 'COUNT':
          console.log(`COUNT result: ${values.length}`)
          return values.length.toString()
        case 'MIN':
          const min = values.length > 0 ? Math.min(...values) : 0
          console.log(`MIN result: ${min}`)
          return min.toString()
        case 'MAX':
          const max = values.length > 0 ? Math.max(...values) : 0
          console.log(`MAX result: ${max}`)
          return max.toString()
        default:
          return '0'
      }
    }

    // Handle single cell reference
    const cellMatch = args.match(/^([A-Z]+)(\d+)$/)
    if (cellMatch) {
      const cellValue = getCellValue(args)
      console.log(`Single cell ${args} value: ${cellValue}`)

      switch (functionName.toUpperCase()) {
        case 'SUM':
        case 'AVERAGE':
        case 'MIN':
        case 'MAX':
          return typeof cellValue === 'number' ? cellValue.toString() : '0'
        case 'COUNT':
          return (typeof cellValue === 'number' || (typeof cellValue === 'string' && !isNaN(Number(cellValue)))) ? '1' : '0'
        default:
          return '0'
      }
    }

    // Handle column reference (like A:A)
    const columnMatch = args.match(/^([A-Z]+):([A-Z]+)$/)
    if (columnMatch && columnMatch[1] === columnMatch[2]) {
      const actualColumn = columnMapping.mapping[columnMatch[1]]
      if (actualColumn && columnStats[actualColumn]) {
        switch (functionName.toUpperCase()) {
          case 'SUM':
            return columnStats[actualColumn].sum.toString()
          case 'AVERAGE':
            return columnStats[actualColumn].avg.toString()
          case 'COUNT':
            return columnStats[actualColumn].numericCount.toString()
          case 'MIN':
            return columnStats[actualColumn].min.toString()
          case 'MAX':
            return columnStats[actualColumn].max.toString()
          default:
            return '0'
        }
      }
    }

    // Handle comma-separated values
    if (args.includes(',')) {
      const items = args.split(',').map(item => item.trim())
      const values: number[] = []

      for (const item of items) {
        if (item.match(/^[A-Z]+\d+$/)) {
          // Cell reference
          const cellValue = getCellValue(item)
          if (typeof cellValue === 'number') {
            values.push(cellValue)
          } else if (typeof cellValue === 'string' && !isNaN(Number(cellValue))) {
            values.push(Number(cellValue))
          }
        } else if (!isNaN(Number(item))) {
          // Direct number
          values.push(Number(item))
        }
      }

      switch (functionName.toUpperCase()) {
        case 'SUM':
          return values.reduce((acc, val) => acc + val, 0).toString()
        case 'AVERAGE':
          return values.length > 0 ? (values.reduce((acc, val) => acc + val, 0) / values.length).toString() : '0'
        case 'COUNT':
          return values.length.toString()
        case 'MIN':
          return values.length > 0 ? Math.min(...values).toString() : '0'
        case 'MAX':
          return values.length > 0 ? Math.max(...values).toString() : '0'
        default:
          return '0'
      }
    }

    // Handle direct number
    if (!isNaN(Number(args))) {
      const num = Number(args)
      switch (functionName.toUpperCase()) {
        case 'SUM':
        case 'AVERAGE':
        case 'MIN':
        case 'MAX':
          return num.toString()
        case 'COUNT':
          return '1'
        default:
          return '0'
      }
    }

    console.log(`${functionName} fallback to 0 for: ${args}`)
    return '0'
  }, [getCellValue, getColumnIndex, getExcelColumnName, columnMapping, columnStats, data, columns])

  // Advanced formula validation
  const validateFormula = useCallback((formulaText: string): string[] => {
    const errors: string[] = []

    // Check for basic syntax errors
    if (formulaText.trim() === '') return errors

    // Check for balanced parentheses
    let parenCount = 0
    for (const char of formulaText) {
      if (char === '(') parenCount++
      if (char === ')') parenCount--
      if (parenCount < 0) {
        errors.push('Unmatched closing parenthesis')
        break
      }
    }
    if (parenCount > 0) {
      errors.push('Unmatched opening parenthesis')
    }

    // Check for valid cell references
    const cellRefs = formulaText.match(/[A-Z]+\d+/g) || []
    for (const cellRef of cellRefs) {
      const parsed = parseCellReference(cellRef)
      if (!parsed) {
        errors.push(`Invalid cell reference: ${cellRef}`)
      }
    }

    // Check for valid column references
    const colRefs = formulaText.match(/[A-Z]+:[A-Z]+/g) || []
    for (const colRef of colRefs) {
      const [startCol, endCol] = colRef.split(':')
      if (!columnMapping.mapping[startCol] || !columnMapping.mapping[endCol]) {
        errors.push(`Invalid column reference: ${colRef}`)
      }
    }

    // Check for division by zero
    if (formulaText.includes('/0') || formulaText.includes('/ 0')) {
      errors.push('Division by zero detected')
    }

    return errors
  }, [parseCellReference, columnMapping])

  // Enhanced error handling for different error types
  const handleCalculationError = useCallback((error: any): string => {
    if (error.message) {
      // Math.js specific errors
      if (error.message.includes('Unexpected type')) {
        return 'Type mismatch: Check that you\'re using numbers where expected'
      }
      if (error.message.includes('Undefined symbol')) {
        return 'Unknown function or variable'
      }
      if (error.message.includes('Unexpected token')) {
        return 'Syntax error: Check your formula syntax'
      }
      if (error.message.includes('Division by zero')) {
        return 'Division by zero error'
      }
      if (error.message.includes('out of range')) {
        return 'Value out of range'
      }
      return error.message
    }
    return 'Unknown calculation error'
  }, [])

  // Calculate comprehensive column statistics for reference
  const columnStats = useMemo(() => {
    const stats: Record<string, any> = {}

    columns.forEach((column, index) => {
      const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined)
      const numericValues = values.filter(val => typeof val === 'number' || !isNaN(Number(val))).map(Number)
      const textValues = values.filter(val => typeof val === 'string')

      // Calculate median
      const sortedNumeric = [...numericValues].sort((a, b) => a - b)
      const median = sortedNumeric.length > 0
        ? sortedNumeric.length % 2 === 0
          ? (sortedNumeric[sortedNumeric.length / 2 - 1] + sortedNumeric[sortedNumeric.length / 2]) / 2
          : sortedNumeric[Math.floor(sortedNumeric.length / 2)]
        : 0

      // Calculate mode
      const frequency: Record<string, number> = {}
      values.forEach(val => {
        const key = String(val)
        frequency[key] = (frequency[key] || 0) + 1
      })
      const mode = Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, '')

      // Calculate standard deviation and variance
      const mean = numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : 0
      const variance = numericValues.length > 0
        ? numericValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numericValues.length
        : 0
      const stdev = Math.sqrt(variance)

      // Calculate product
      const product = numericValues.length > 0 ? numericValues.reduce((a, b) => a * b, 1) : 0

      const excelColumn = getExcelColumnName(index)

      stats[column] = {
        count: values.length,
        counta: values.length,
        numericCount: numericValues.length,
        textCount: textValues.length,
        sum: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) : 0,
        product,
        avg: mean,
        median,
        mode,
        min: numericValues.length > 0 ? Math.min(...numericValues) : 0,
        max: numericValues.length > 0 ? Math.max(...numericValues) : 0,
        stdev,
        variance,
        type: numericValues.length > 0 ? 'number' : 'text',
        values: numericValues,
        allValues: values,
        excelColumn
      }
    })

    return stats
  }, [data, columns, columnMapping])

  // Excel-like formula evaluation with exact Excel syntax
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    setIsCalculating(true)

    try {
      let processedFormula = formulaText.trim()

      // Pre-validation
      const validationErrors = validateFormula(formulaText)
      if (validationErrors.length > 0) {
        setValidationErrors(validationErrors)
        return {
          formula: formulaText,
          result: null,
          error: validationErrors[0],
          timestamp: Date.now()
        }
      }

      setValidationErrors([])

      // Remove leading = if present (Excel style)
      if (processedFormula.startsWith('=')) {
        processedFormula = processedFormula.substring(1)
      }

      console.log(`Original formula: ${formulaText}`)
      console.log(`Processing formula: ${processedFormula}`)

      // First, handle Excel functions before replacing cell references
      // This ensures functions get the raw range references, not processed arrays

      // Process SUM function with proper Excel behavior
      processedFormula = processedFormula.replace(/SUM\(([^)]+)\)/gi, (match, args) => {
        console.log(`Processing SUM function with args: ${args}`)
        return evaluateExcelFunction('SUM', args)
      })

      // Process AVERAGE function
      processedFormula = processedFormula.replace(/AVERAGE\(([^)]+)\)/gi, (match, args) => {
        console.log(`Processing AVERAGE function with args: ${args}`)
        return evaluateExcelFunction('AVERAGE', args)
      })

      // Process COUNT function
      processedFormula = processedFormula.replace(/COUNT\(([^)]+)\)/gi, (match, args) => {
        console.log(`Processing COUNT function with args: ${args}`)
        return evaluateExcelFunction('COUNT', args)
      })

      // Process MIN function
      processedFormula = processedFormula.replace(/MIN\(([^)]+)\)/gi, (match, args) => {
        console.log(`Processing MIN function with args: ${args}`)
        return evaluateExcelFunction('MIN', args)
      })

      // Process MAX function
      processedFormula = processedFormula.replace(/MAX\(([^)]+)\)/gi, (match, args) => {
        console.log(`Processing MAX function with args: ${args}`)
        return evaluateExcelFunction('MAX', args)
      })

      // After processing functions, replace any remaining cell references with values
      processedFormula = processedFormula.replace(/([A-Z]+)(\d+)/g, (match, col, row) => {
        const cellValue = getCellValue(match)
        console.log(`Replacing cell ${match} with value: ${cellValue}`)
        return typeof cellValue === 'number' ? cellValue.toString() : `"${cellValue}"`
      })

      console.log(`Formula after function processing: ${processedFormula}`)

      // Use math.js to evaluate the final expression
      console.log(`Final formula to evaluate: ${processedFormula}`)

      // If the formula is just a number after processing, return it directly
      if (!isNaN(Number(processedFormula))) {
        const numResult = Number(processedFormula)
        return {
          formula: formulaText,
          result: numResult,
          timestamp: Date.now()
        }
      }

      // Otherwise evaluate with math.js
      const result = math.evaluate(processedFormula)

      // Post-process result for Excel compatibility
      let finalResult = result
      if (typeof finalResult === 'number') {
        // Handle Excel-like number formatting
        if (isNaN(finalResult)) finalResult = '#NUM!'
        if (!isFinite(finalResult)) finalResult = '#DIV/0!'
        if (finalResult > 1e15) finalResult = '#NUM!'
        if (finalResult < -1e15) finalResult = '#NUM!'
      }

      return {
        formula: formulaText,
        result: finalResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      const enhancedError = handleCalculationError(error)
      return {
        formula: formulaText,
        result: null,
        error: enhancedError,
        timestamp: Date.now()
      }
    } finally {
      setIsCalculating(false)
    }
  }, [evaluateExcelFunction, getCellValue, validateFormula, handleCalculationError])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    if (calculationResult.result !== null && !calculationResult.error) {
      const historyItem: FormulaHistory = {
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert function into formula
  const insertFunction = (func: FormulaFunction) => {
    const insertion = func.syntax
    setFormula(prev => prev + insertion)
    setSelectedFunction(func)
  }

  // Insert column reference
  const insertColumn = (column: string) => {
    setFormula(prev => prev + column)
  }

  // Insert text at cursor position
  const insertAtCursor = useCallback((text: string) => {
    const input = formulaInputRef.current
    if (input) {
      const start = input.selectionStart || 0
      const end = input.selectionEnd || 0
      const currentFormula = formula
      const newFormula = currentFormula.slice(0, start) + text + currentFormula.slice(end)
      setFormula(newFormula)

      // Set cursor position after inserted text
      setTimeout(() => {
        if (input) {
          input.focus()
          input.setSelectionRange(start + text.length, start + text.length)
        }
      }, 0)
    } else {
      // Fallback if no input ref
      setFormula(prev => prev + text)
    }
  }, [formula])

  // Add cell reference to formula (called from table clicks)
  const addCellReference = useCallback((cellRef: string) => {
    insertAtCursor(cellRef)
  }, [insertAtCursor])

  // Add column reference to formula (called from column header clicks)
  const addColumnReference = useCallback((columnRef: string) => {
    insertAtCursor(columnRef)
  }, [insertAtCursor])

  // Handle cursor position changes
  const handleCursorChange = useCallback((e: any) => {
    setCursorPosition(e.target.selectionStart || 0)
  }, [])

  // Clear formula and result
  const clearFormula = () => {
    setFormula('')
    setResult(null)
    setValidationErrors([])
  }

  // Copy result to clipboard
  const copyResult = () => {
    if (result && result.result !== null) {
      navigator.clipboard.writeText(String(result.result))
      toast.success('Result copied to clipboard')
    }
  }

  // Open save dialog with pre-filled values
  const openSaveDialog = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save. Please calculate a formula first.')
      return
    }

    // Pre-fill the title and description
    const defaultTitle = formula.length > 30
      ? `${formula.substring(0, 30)}...`
      : formula

    setSaveTitle(defaultTitle)
    setSaveDescription(`Result: ${result.result}`)
    setShowSaveDialog(true)
  }

  // Save calculator result to dashboard
  const saveToDashboard = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save.')
      return
    }

    if (!saveTitle.trim()) {
      toast.error('Please enter a title for the calculation.')
      return
    }

    // Call the save function from useChartSaving hook with icon
    handleSaveCalculatorResult(
      formula,
      result.result,
      saveTitle,
      saveDescription,
      selectedIcon
    )

    setShowSaveDialog(false)
    setSaveTitle('')
    setSaveDescription('')
    setSelectedIcon('Calculator') // Reset to default
  }

  return (
    <div className="w-full max-w-full overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-blue-600 flex-shrink-0" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Excel Calculator</h3>
        </div>
            console.log(`SUM array values: [${values.join(',')}]`)
            const sum = values.reduce((sum: number, val: number) => sum + val, 0)
            console.log(`SUM result: ${sum}`)
            return sum.toString()
          }
          
          // Check if it's a cell reference (like H1)
          const cellRefMatch = args.match(/^([A-Z]+)(\d+)$/)
          if (cellRefMatch) {
            const cellValue = getCellValue(args)
            console.log(`SUM single cell ${args} value: ${cellValue}`)
            return typeof cellValue === 'number' ? cellValue.toString() : '0'
          }
          
          // Check if it's a range that wasn't processed (direct function call)
          const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
          if (rangeMatch) {
            console.log(`Direct SUM range: ${args}`)
            const [_, startCol, startRow, endCol, endRow] = rangeMatch
            
            const startColIndex = getColumnIndex(startCol)
            const endColIndex = getColumnIndex(endCol)
            const startRowIndex = parseInt(startRow) - 1
            const endRowIndex = parseInt(endRow) - 1
            
            // Handle reversed ranges
            const minColIndex = Math.min(startColIndex, endColIndex)
            const maxColIndex = Math.max(startColIndex, endColIndex)
            const minRowIndex = Math.min(startRowIndex, endRowIndex)
            const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)
            
            let sum = 0
            for (let row = minRowIndex; row <= maxRowIndex; row++) {
              if (row < 0) continue;
              
              for (let col = minColIndex; col <= maxColIndex; col++) {
                if (col < 0 || col >= columns.length) continue;
                
                const columnName = getExcelColumnName(col)
                const cellRef = `${columnName}${row + 1}`
                const value = getCellValue(cellRef)
                
                if (typeof value === 'number') {
                  sum += value
                } else if (typeof value === 'string' && !isNaN(Number(value))) {
                  sum += Number(value)
                }
              }
            }
            
            console.log(`Direct SUM range result: ${sum}`)
            return sum.toString()
          }
          
          // If it's a column name, get the sum
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            console.log(`SUM column ${args} (${actualColumn}) result: ${columnStats[actualColumn].sum}`)
            return columnStats[actualColumn].sum.toString()
          }
          
          // Check if it's a direct number or a range of numbers (like 18425:7015)
          const directNumbersMatch = args.match(/^(\d+):(\d+)$/)
          if (directNumbersMatch) {
            console.log(`Direct number range: ${args}`)
            const [_, startNum, endNum] = directNumbersMatch
            
            // In Excel, when you type SUM(18425:7015), it actually tries to interpret
            // these as cell references in a specific row, not as a range of integers to sum.
            // Since we've already processed cell references, and these appear to be direct numbers,
            // we'll just add these two specific numbers rather than summing all integers in between.
            const num1 = parseInt(startNum)
            const num2 = parseInt(endNum)
            const sum = num1 + num2
            
            console.log(`Direct number sum: ${sum}`)
            return sum.toString()
          }
          
          // Check if it's a comma-separated list of values or cell references
          if (args.includes(',')) {
            console.log(`Comma-separated list: ${args}`)
            const items = args.split(',').map((item:any) => item.trim())
            let sum = 0
            
            for (const item of items) {
              // Check if it's a cell reference
              if (item.match(/^[A-Z]+\d+$/)) {
                const cellValue = getCellValue(item)
                if (typeof cellValue === 'number') {
                  sum += cellValue
                } else if (typeof cellValue === 'string' && !isNaN(Number(cellValue))) {
                  sum += Number(cellValue)
                }
              } 
              // Check if it's a direct number
              else if (!isNaN(Number(item))) {
                sum += Number(item)
              }
            }
            
            console.log(`Comma-separated list sum: ${sum}`)
            return sum.toString()
          }
          
          // Check if it's a direct number
          if (!isNaN(Number(args))) {
            console.log(`Direct number: ${args}`)
            return args
          }
          
          console.log(`SUM fallback to 0 for: ${args}`)
          return '0'
        })

        // AVERAGE function
        .replace(/AVERAGE\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing AVERAGE function with args: ${args}`)
          
          // If it's already processed as an array, average it
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            console.log(`AVERAGE array values: [${values.join(',')}]`)
            if (values.length === 0) return '0'
            const avg = values.reduce((sum: number, val: number) => sum + val, 0) / values.length
            console.log(`AVERAGE result: ${avg}`)
            return avg.toString()
          }
          
          // Check if it's a cell reference (like H1)
          const cellRefMatch = args.match(/^([A-Z]+)(\d+)$/)
          if (cellRefMatch) {
            const cellValue = getCellValue(args)
            console.log(`AVERAGE single cell ${args} value: ${cellValue}`)
            return typeof cellValue === 'number' ? cellValue.toString() : '0'
          }
          
          // Check if it's a range that wasn't processed (direct function call)
          const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
          if (rangeMatch) {
            console.log(`Direct AVERAGE range: ${args}`)
            const [_, startCol, startRow, endCol, endRow] = rangeMatch
            
            const startColIndex = getColumnIndex(startCol)
            const endColIndex = getColumnIndex(endCol)
            const startRowIndex = parseInt(startRow) - 1
            const endRowIndex = parseInt(endRow) - 1
            
            // Handle reversed ranges
            const minColIndex = Math.min(startColIndex, endColIndex)
            const maxColIndex = Math.max(startColIndex, endColIndex)
            const minRowIndex = Math.min(startRowIndex, endRowIndex)
            const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)
            
            let sum = 0
            let count = 0
            for (let row = minRowIndex; row <= maxRowIndex; row++) {
              if (row < 0) continue;
              
              for (let col = minColIndex; col <= maxColIndex; col++) {
                if (col < 0 || col >= columns.length) continue;
                
                const columnName = getExcelColumnName(col)
                const cellRef = `${columnName}${row + 1}`
                const value = getCellValue(cellRef)
                
                if (typeof value === 'number') {
                  sum += value
                  count++
                } else if (typeof value === 'string' && !isNaN(Number(value))) {
                  sum += Number(value)
                  count++
                }
              }
            }
            
            const avg = count > 0 ? sum / count : 0
            console.log(`Direct AVERAGE range result: ${avg}`)
            return avg.toString()
          }
          
          // If it's a column name, get the average
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            console.log(`AVERAGE column ${args} (${actualColumn}) result: ${columnStats[actualColumn].avg}`)
            return columnStats[actualColumn].avg.toString()
          }
          
          // Check if it's a direct number or a range of numbers (like 18425:7015)
          const directNumbersMatch = args.match(/^(\d+):(\d+)$/)
          if (directNumbersMatch) {
            console.log(`Direct number range for AVERAGE: ${args}`)
            const [_, startNum, endNum] = directNumbersMatch
            
            // Match Excel behavior for direct numbers
            const num1 = parseInt(startNum)
            const num2 = parseInt(endNum)
            const avg = (num1 + num2) / 2
            
            console.log(`Direct number average: ${avg}`)
            return avg.toString()
          }
          
          // Check if it's a comma-separated list of values or cell references
          if (args.includes(',')) {
            console.log(`Comma-separated list for AVERAGE: ${args}`)
            const items = args.split(',').map((item:any) => item.trim())
            let sum = 0
            let count = 0
            
            for (const item of items) {
              // Check if it's a cell reference
              if (item.match(/^[A-Z]+\d+$/)) {
                const cellValue = getCellValue(item)
                if (typeof cellValue === 'number') {
                  sum += cellValue
                  count++
                } else if (typeof cellValue === 'string' && !isNaN(Number(cellValue))) {
                  sum += Number(cellValue)
                  count++
                }
              } 
              // Check if it's a direct number
              else if (!isNaN(Number(item))) {
                sum += Number(item)
                count++
              }
            }
            
            const avg = count > 0 ? sum / count : 0
            console.log(`Comma-separated list average: ${avg}`)
            return avg.toString()
          }
          
          // Check if it's a direct number
          if (!isNaN(Number(args))) {
            console.log(`Direct number for AVERAGE: ${args}`)
            return args
          }
          
          console.log(`AVERAGE fallback to 0 for: ${args}`)
          return '0'
        })

        // COUNT function
        .replace(/COUNT\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing COUNT function with args: ${args}`)
          
          // If it's already processed as an array, count it
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            console.log(`COUNT array values: [${values.join(',')}]`)
            console.log(`COUNT result: ${values.length}`)
            return values.length.toString()
          }
          
          // Check if it's a cell reference (like H1)
          const cellRefMatch = args.match(/^([A-Z]+)(\d+)$/)
          if (cellRefMatch) {
            const cellValue = getCellValue(args)
            console.log(`COUNT single cell ${args} value: ${cellValue}`)
            return (typeof cellValue === 'number' || (typeof cellValue === 'string' && !isNaN(Number(cellValue)))) ? '1' : '0'
          }
          
          // Check if it's a range that wasn't processed (direct function call)
          const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
          if (rangeMatch) {
            console.log(`Direct COUNT range: ${args}`)
            const [_, startCol, startRow, endCol, endRow] = rangeMatch
            
            const startColIndex = getColumnIndex(startCol)
            const endColIndex = getColumnIndex(endCol)
            const startRowIndex = parseInt(startRow) - 1
            const endRowIndex = parseInt(endRow) - 1
            
            // Handle reversed ranges
            const minColIndex = Math.min(startColIndex, endColIndex)
            const maxColIndex = Math.max(startColIndex, endColIndex)
            const minRowIndex = Math.min(startRowIndex, endRowIndex)
            const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)
            
            let count = 0
            for (let row = minRowIndex; row <= maxRowIndex; row++) {
              if (row < 0) continue;
              
              for (let col = minColIndex; col <= maxColIndex; col++) {
                if (col < 0 || col >= columns.length) continue;
                
                const columnName = getExcelColumnName(col)
                const cellRef = `${columnName}${row + 1}`
                const value = getCellValue(cellRef)
                
                if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
                  count++
                }
              }
            }
            
            console.log(`Direct COUNT range result: ${count}`)
            return count.toString()
          }
          
          // If it's a column name, get the count
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            console.log(`COUNT column ${args} (${actualColumn}) result: ${columnStats[actualColumn].numericCount}`)
            return columnStats[actualColumn].numericCount.toString()
          }
          
          // Check if it's a direct number or a range of numbers (like 18425:7015)
          const directNumbersMatch = args.match(/^(\d+):(\d+)$/)
          if (directNumbersMatch) {
            console.log(`Direct number range for COUNT: ${args}`)
            const [_, startNum, endNum] = directNumbersMatch
            
            // Match Excel behavior for direct numbers - count is always 2 for two numbers
            console.log(`Direct number count: 2`)
            return '2'
          }
          
          // Check if it's a comma-separated list of values or cell references
          if (args.includes(',')) {
            console.log(`Comma-separated list for COUNT: ${args}`)
            const items = args.split(',').map((item:any) => item.trim())
            let count = 0
            
            for (const item of items) {
              // Check if it's a cell reference
              if (item.match(/^[A-Z]+\d+$/)) {
                const cellValue = getCellValue(item)
                if (typeof cellValue === 'number' || (typeof cellValue === 'string' && !isNaN(Number(cellValue)))) {
                  count++
                }
              } 
              // Check if it's a direct number
              else if (!isNaN(Number(item))) {
                count++
              }
            }
            
            console.log(`Comma-separated list count: ${count}`)
            return count.toString()
          }
          
          // Check if it's a direct number
          if (!isNaN(Number(args))) {
            console.log(`Direct number for COUNT: ${args}`)
            return '1'
          }
          
          console.log(`COUNT fallback to 0 for: ${args}`)
          return '0'
        })

        // MIN function
        .replace(/MIN\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MIN function with args: ${args}`)
          
          // If it's already processed as an array, find minimum
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            console.log(`MIN array values: [${values.join(',')}]`)
            if (values.length === 0) return '0'
            const min = Math.min(...values)
            console.log(`MIN result: ${min}`)
            return min.toString()
          }
          
          // Check if it's a cell reference (like H1)
          const cellRefMatch = args.match(/^([A-Z]+)(\d+)$/)
          if (cellRefMatch) {
            const cellValue = getCellValue(args)
            console.log(`MIN single cell ${args} value: ${cellValue}`)
            return typeof cellValue === 'number' ? cellValue.toString() : '0'
          }
          
          // Check if it's a range that wasn't processed (direct function call)
          const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
          if (rangeMatch) {
            console.log(`Direct MIN range: ${args}`)
            const [_, startCol, startRow, endCol, endRow] = rangeMatch
            
            const startColIndex = getColumnIndex(startCol)
            const endColIndex = getColumnIndex(endCol)
            const startRowIndex = parseInt(startRow) - 1
            const endRowIndex = parseInt(endRow) - 1
            
            // Handle reversed ranges
            const minColIndex = Math.min(startColIndex, endColIndex)
            const maxColIndex = Math.max(startColIndex, endColIndex)
            const minRowIndex = Math.min(startRowIndex, endRowIndex)
            const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)
            
            const values: number[] = []
            for (let row = minRowIndex; row <= maxRowIndex; row++) {
              if (row < 0) continue;
              
              for (let col = minColIndex; col <= maxColIndex; col++) {
                if (col < 0 || col >= columns.length) continue;
                
                const columnName = getExcelColumnName(col)
                const cellRef = `${columnName}${row + 1}`
                const value = getCellValue(cellRef)
                
                if (typeof value === 'number') {
                  values.push(value)
                } else if (typeof value === 'string' && !isNaN(Number(value))) {
                  values.push(Number(value))
                }
              }
            }
            
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`Direct MIN range result: ${min}`)
            return min.toString()
          }
          
          // If it's a column name, get the min
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            console.log(`MIN column ${args} (${actualColumn}) result: ${columnStats[actualColumn].min}`)
            return columnStats[actualColumn].min.toString()
          }
          
          // Check if it's a direct number or a range of numbers (like 18425:7015)
          const directNumbersMatch = args.match(/^(\d+):(\d+)$/)
          if (directNumbersMatch) {
            console.log(`Direct number range for MIN: ${args}`)
            const [_, startNum, endNum] = directNumbersMatch
            
            // Match Excel behavior for direct numbers
            const num1 = parseInt(startNum)
            const num2 = parseInt(endNum)
            const min = Math.min(num1, num2)
            
            console.log(`Direct number min: ${min}`)
            return min.toString()
          }
          
          // Check if it's a comma-separated list of values or cell references
          if (args.includes(',')) {
            console.log(`Comma-separated list for MIN: ${args}`)
            const items = args.split(',').map((item:any) => item.trim())
            const values: number[] = []
            
            for (const item of items) {
              // Check if it's a cell reference
              if (item.match(/^[A-Z]+\d+$/)) {
                const cellValue = getCellValue(item)
                if (typeof cellValue === 'number') {
                  values.push(cellValue)
                } else if (typeof cellValue === 'string' && !isNaN(Number(cellValue))) {
                  values.push(Number(cellValue))
                }
              } 
              // Check if it's a direct number
              else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`Comma-separated list min: ${min}`)
            return min.toString()
          }
          
          // Check if it's a direct number
          if (!isNaN(Number(args))) {
            console.log(`Direct number for MIN: ${args}`)
            return args
          }
          
          console.log(`MIN fallback to 0 for: ${args}`)
          return '0'
        })

        // MAX function
        .replace(/MAX\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MAX function with args: ${args}`)
          
          // If it's already processed as an array, find maximum
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            console.log(`MAX array values: [${values.join(',')}]`)
            if (values.length === 0) return '0'
            const max = Math.max(...values)
            console.log(`MAX result: ${max}`)
            return max.toString()
          }
          
          // Check if it's a cell reference (like H1)
          const cellRefMatch = args.match(/^([A-Z]+)(\d+)$/)
          if (cellRefMatch) {
            const cellValue = getCellValue(args)
            console.log(`MAX single cell ${args} value: ${cellValue}`)
            return typeof cellValue === 'number' ? cellValue.toString() : '0'
          }
          
          // Check if it's a range that wasn't processed (direct function call)
          const rangeMatch = args.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
          if (rangeMatch) {
            console.log(`Direct MAX range: ${args}`)
            const [_, startCol, startRow, endCol, endRow] = rangeMatch
            
            const startColIndex = getColumnIndex(startCol)
            const endColIndex = getColumnIndex(endCol)
            const startRowIndex = parseInt(startRow) - 1
            const endRowIndex = parseInt(endRow) - 1
            
            // Handle reversed ranges
            const minColIndex = Math.min(startColIndex, endColIndex)
            const maxColIndex = Math.max(startColIndex, endColIndex)
            const minRowIndex = Math.min(startRowIndex, endRowIndex)
            const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)
            
            const values: number[] = []
            for (let row = minRowIndex; row <= maxRowIndex; row++) {
              if (row < 0) continue;
              
              for (let col = minColIndex; col <= maxColIndex; col++) {
                if (col < 0 || col >= columns.length) continue;
                
                const columnName = getExcelColumnName(col)
                const cellRef = `${columnName}${row + 1}`
                const value = getCellValue(cellRef)
                
                if (typeof value === 'number') {
                  values.push(value)
                } else if (typeof value === 'string' && !isNaN(Number(value))) {
                  values.push(Number(value))
                }
              }
            }
            
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`Direct MAX range result: ${max}`)
            return max.toString()
          }
          
          // If it's a column name, get the max
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            console.log(`MAX column ${args} (${actualColumn}) result: ${columnStats[actualColumn].max}`)
            return columnStats[actualColumn].max.toString()
          }
          
          // Check if it's a direct number or a range of numbers (like 18425:7015)
          const directNumbersMatch = args.match(/^(\d+):(\d+)$/)
          if (directNumbersMatch) {
            console.log(`Direct number range for MAX: ${args}`)
            const [_, startNum, endNum] = directNumbersMatch
            
            // Match Excel behavior for direct numbers
            const num1 = parseInt(startNum)
            const num2 = parseInt(endNum)
            const max = Math.max(num1, num2)
            
            console.log(`Direct number max: ${max}`)
            return max.toString()
          }
          
          // Check if it's a comma-separated list of values or cell references
          if (args.includes(',')) {
            console.log(`Comma-separated list for MAX: ${args}`)
            const items = args.split(',').map((item:any) => item.trim())
            const values: number[] = []
            
            for (const item of items) {
              // Check if it's a cell reference
              if (item.match(/^[A-Z]+\d+$/)) {
                const cellValue = getCellValue(item)
                if (typeof cellValue === 'number') {
                  values.push(cellValue)
                } else if (typeof cellValue === 'string' && !isNaN(Number(cellValue))) {
                  values.push(Number(cellValue))
                }
              } 
              // Check if it's a direct number
              else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`Comma-separated list max: ${max}`)
            return max.toString()
          }
          
          // Check if it's a direct number
          if (!isNaN(Number(args))) {
            console.log(`Direct number for MAX: ${args}`)
            return args
          }
          
          console.log(`MAX fallback to 0 for: ${args}`)
          return '0'
        })

        // ROUND function
        .replace(/ROUND\(([^,]+),\s*([^)]+)\)/gi, (match, number, decimals) => {
          return `round(${number}, ${decimals})`
        })

        // IF function
        .replace(/IF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, condition, trueVal, falseVal) => {
          return `(${condition}) ? (${trueVal}) : (${falseVal})`
        })

        // AND function
        .replace(/AND\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' && ')})`
        })

        // OR function
        .replace(/OR\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' || ')})`
        })

        // CONCATENATE function
        .replace(/CONCATENATE\(([^)]+)\)/gi, (match, args) => {
          const parts = args.split(',').map((p: string) => p.trim())
          return parts.join(' + ')
        })

        // Mathematical functions
        .replace(/POWER\(([^,]+),\s*([^)]+)\)/gi, (match, base, exp) => `pow(${base}, ${exp})`)
        .replace(/SQRT\(([^)]+)\)/gi, (match, num) => `sqrt(${num})`)
        .replace(/ABS\(([^)]+)\)/gi, (match, num) => `abs(${num})`)
        .replace(/EXP\(([^)]+)\)/gi, (match, num) => `exp(${num})`)
        .replace(/LN\(([^)]+)\)/gi, (match, num) => `log(${num})`)
        .replace(/LOG10\(([^)]+)\)/gi, (match, num) => `log10(${num})`)
        .replace(/SIN\(([^)]+)\)/gi, (match, num) => `sin(${num})`)
        .replace(/COS\(([^)]+)\)/gi, (match, num) => `cos(${num})`)
        .replace(/TAN\(([^)]+)\)/gi, (match, num) => `tan(${num})`)
        .replace(/PI\(\)/gi, () => 'pi')

        // Statistical functions
        .replace(/MEDIAN\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n)).sort((a: number, b: number) => a - b)
            if (values.length === 0) return '0'
            const mid = Math.floor(values.length / 2)
            return values.length % 2 === 0
              ? ((values[mid - 1] + values[mid]) / 2).toString()
              : values[mid].toString()
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].median.toString()
          }
          return '0'
        })
        .replace(/STDEV\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            if (values.length <= 1) return '0'
            const mean = values.reduce((sum: number, val: number) => sum + val, 0) / values.length
            const variance = values.reduce((acc: number, val: number) => acc + Math.pow(val - mean, 2), 0) / (values.length - 1)
            return Math.sqrt(variance).toString()
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].stdev.toString()
          }
          return '0'
        })

        // Text functions
        .replace(/LEFT\(([^,]+),\s*([^)]+)\)/gi, (match, text, num) => `substring(${text}, 0, ${num})`)
        .replace(/RIGHT\(([^,]+),\s*([^)]+)\)/gi, (match, text, num) => `substring(${text}, length(${text}) - ${num})`)
        .replace(/MID\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, text, start, length) => `substring(${text}, ${start} - 1, ${start} - 1 + ${length})`)
        .replace(/UPPER\(([^)]+)\)/gi, (match, text) => `uppercase(${text})`)
        .replace(/LOWER\(([^)]+)\)/gi, (match, text) => `lowercase(${text})`)
        .replace(/LEN\(([^)]+)\)/gi, (match, text) => `length(${text})`)
        .replace(/TRIM\(([^)]+)\)/gi, (match, text) => `trim(${text})`)

        // Logical functions
        .replace(/NOT\(([^)]+)\)/gi, (match, condition) => `!(${condition})`)
        .replace(/ISNUMBER\(([^)]+)\)/gi, (match, value) => `isNumber(${value})`)
        .replace(/ISTEXT\(([^)]+)\)/gi, (match, value) => `isText(${value})`)
        .replace(/ISBLANK\(([^)]+)\)/gi, (match, value) => `isBlank(${value})`)

        // Date functions
        .replace(/TODAY\(\)/gi, () => `"${new Date().toISOString().split('T')[0]}"`)
        .replace(/NOW\(\)/gi, () => `"${new Date().toISOString()}"`)
        .replace(/YEAR\(([^)]+)\)/gi, (match, date) => `year(${date})`)
        .replace(/MONTH\(([^)]+)\)/gi, (match, date) => `month(${date})`)
        .replace(/DAY\(([^)]+)\)/gi, (match, date) => `day(${date})`)
        .replace(/WEEKDAY\(([^)]+)\)/gi, (match, date) => `weekday(${date})`)

        // Financial functions
        .replace(/PMT\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, rate, nper, pv) => `pmt(${rate}, ${nper}, ${pv})`)
        .replace(/FV\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, rate, nper, pmt, pv) => `fv(${rate}, ${nper}, ${pmt}, ${pv})`)

        // Advanced Excel functions
        .replace(/SUMIF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, range, criteria, sumRange) => {
          return `sumif(${range}, ${criteria}, ${sumRange})`
        })
        .replace(/AVERAGEIF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, range, criteria, avgRange) => {
          return `averageif(${range}, ${criteria}, ${avgRange})`
        })
        .replace(/SUMIFS\(([^)]+)\)/gi, (match, args) => {
          return `sumifs(${args})`
        })
        .replace(/INDEX\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, array, row, col) => {
          return `index(${array}, ${row}, ${col})`
        })
        .replace(/MATCH\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, array, type) => {
          return `match(${lookup}, ${array}, ${type})`
        })
        .replace(/VLOOKUP\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, table, col, exact) => {
          return `vlookup(${lookup}, ${table}, ${col}, ${exact})`
        })
        .replace(/HLOOKUP\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, table, row, exact) => {
          return `hlookup(${lookup}, ${table}, ${row}, ${exact})`
        })

        // Array functions
        .replace(/TRANSPOSE\(([^)]+)\)/gi, (match, array) => {
          return `transpose(${array})`
        })
        .replace(/SORT\(([^)]+)\)/gi, (match, array) => {
          return `sort(${array})`
        })
        .replace(/UNIQUE\(([^)]+)\)/gi, (match, array) => {
          return `unique(${array})`
        })

        // Advanced statistical functions
        .replace(/PERCENTILE\(([^,]+),\s*([^)]+)\)/gi, (match, array, k) => {
          return `percentile(${array}, ${k})`
        })
        .replace(/QUARTILE\(([^,]+),\s*([^)]+)\)/gi, (match, array, quart) => {
          return `quartile(${array}, ${quart})`
        })
        .replace(/RANK\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, number, ref, order) => {
          return `rank(${number}, ${ref}, ${order})`
        })

        // Advanced math functions
        .replace(/GCD\(([^)]+)\)/gi, (match, args) => {
          return `gcd(${args})`
        })
        .replace(/LCM\(([^)]+)\)/gi, (match, args) => {
          return `lcm(${args})`
        })
        .replace(/FACT\(([^)]+)\)/gi, (match, num) => {
          return `factorial(${num})`
        })
        .replace(/COMBIN\(([^,]+),\s*([^)]+)\)/gi, (match, n, k) => {
          return `combinations(${n}, ${k})`
        })
        .replace(/PERMUT\(([^,]+),\s*([^)]+)\)/gi, (match, n, k) => {
          return `permutations(${n}, ${k})`
        })

      // Create evaluation scope with comprehensive Excel-like functions
      const scope = {
        // Mathematical functions
        round: (num: number, decimals: number) => Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals),
        pow: Math.pow,
        sqrt: Math.sqrt,
        abs: Math.abs,
        exp: Math.exp,
        log: Math.log,
        log10: (num: number) => Math.log10(num),
        sin: Math.sin,
        cos: Math.cos,
        tan: Math.tan,
        pi: Math.PI,
        e: Math.E,

        // Text functions
        length: (str: string) => String(str).length,
        uppercase: (str: string) => String(str).toUpperCase(),
        lowercase: (str: string) => String(str).toLowerCase(),
        substring: (str: string, start: number, end?: number) => String(str).substring(start, end),
        trim: (str: string) => String(str).trim(),

        // Date functions
        year: (date: string) => new Date(date).getFullYear(),
        month: (date: string) => new Date(date).getMonth() + 1,
        day: (date: string) => new Date(date).getDate(),
        weekday: (date: string) => new Date(date).getDay() + 1,

        // Logical functions
        isNumber: (value: any) => typeof value === 'number' && !isNaN(value),
        isText: (value: any) => typeof value === 'string',
        isBlank: (value: any) => value === null || value === undefined || value === '',

        // Financial functions (simplified)
        pmt: (rate: number, nper: number, pv: number) => {
          if (rate === 0) return -pv / nper
          return -pv * (rate * Math.pow(1 + rate, nper)) / (Math.pow(1 + rate, nper) - 1)
        },
        fv: (rate: number, nper: number, pmt: number, pv: number = 0) => {
          if (rate === 0) return -pv - pmt * nper
          return -pv * Math.pow(1 + rate, nper) - pmt * ((Math.pow(1 + rate, nper) - 1) / rate)
        },

        // Advanced Excel functions
        sumif: (range: number[], criteria: any, sumRange?: number[]) => {
          // Simplified SUMIF implementation
          const targetRange = sumRange || range
          return range.reduce((sum, val, i) => {
            if (val === criteria || (typeof criteria === 'string' && String(val).includes(criteria))) {
              return sum + (targetRange[i] || 0)
            }
            return sum
          }, 0)
        },

        averageif: (range: number[], criteria: any, avgRange?: number[]) => {
          const targetRange = avgRange || range
          let sum = 0
          let count = 0
          range.forEach((val, i) => {
            if (val === criteria || (typeof criteria === 'string' && String(val).includes(criteria))) {
              sum += targetRange[i] || 0
              count++
            }
          })
          return count > 0 ? sum / count : 0
        },

        percentile: (array: number[], k: number) => {
          const sorted = [...array].sort((a, b) => a - b)
          const index = (sorted.length - 1) * k
          const lower = Math.floor(index)
          const upper = Math.ceil(index)
          const weight = index % 1

          if (upper >= sorted.length) return sorted[sorted.length - 1]
          return sorted[lower] * (1 - weight) + sorted[upper] * weight
        },

        quartile: (array: number[], quart: number) => {
          const percentiles = [0, 0.25, 0.5, 0.75, 1]
          return scope.percentile(array, percentiles[quart] || 0.5)
        },

        rank: (number: number, ref: number[], order: number = 0) => {
          const sorted = [...ref].sort((a, b) => order === 0 ? b - a : a - b)
          return sorted.indexOf(number) + 1
        },

        factorial: (n: number): number => {
          if (n <= 1) return 1
          return n * scope.factorial(n - 1)
        },

        combinations: (n: number, k: number) => {
          return scope.factorial(n) / (scope.factorial(k) * scope.factorial(n - k))
        },

        permutations: (n: number, k: number) => {
          return scope.factorial(n) / scope.factorial(n - k)
        },

        gcd: (...args: number[]) => {
          const gcdTwo = (a: number, b: number): number => b === 0 ? a : gcdTwo(b, a % b)
          return args.reduce(gcdTwo)
        },

        lcm: (...args: number[]) => {
          const lcmTwo = (a: number, b: number) => Math.abs(a * b) / scope.gcd(a, b)
          return args.reduce(lcmTwo)
        },

        // Lookup functions (simplified)
        vlookup: (lookup: any, table: any, col: number, exact: boolean) => {
          // Simplified implementation - would need more complex logic for real VLOOKUP
          return lookup
        },

        hlookup: (lookup: any, table: any, row: number, exact: boolean) => {
          // Simplified implementation
          return lookup
        },

        index: (array: any[], row: number, col: number) => {
          // Simplified implementation
          return array[row - 1] || null
        },

        match: (lookup: any, array: any[], type: number) => {
          // Simplified implementation
          const index = array.indexOf(lookup)
          return index >= 0 ? index + 1 : null
        }
      }

      const calculatedResult = math.evaluate(processedFormula, scope)

      // Post-process result for Excel compatibility
      let finalResult = calculatedResult
      if (typeof finalResult === 'number') {
        // Handle Excel-like number formatting
        if (isNaN(finalResult)) finalResult = '#NUM!'
        if (!isFinite(finalResult)) finalResult = '#DIV/0!'
        if (finalResult > 1e15) finalResult = '#NUM!'
        if (finalResult < -1e15) finalResult = '#NUM!'
      }

      return {
        formula: formulaText,
        result: finalResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      const enhancedError = handleCalculationError(error)
      return {
        formula: formulaText,
        result: null,
        error: enhancedError,
        timestamp: Date.now()
      }
    } finally {
      setIsCalculating(false)
    }
  }, [columnStats, columnMapping, getCellValue, getColumnIndex, getExcelColumnName, validateFormula, handleCalculationError])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    // Add to history if successful
    if (calculationResult.result !== null) {
      const historyItem: FormulaHistory = {
        id: Date.now().toString(),
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert function into formula
  const insertFunction = (func: FormulaFunction) => {
    const insertion = func.syntax
    setFormula(prev => prev + insertion)
    setSelectedFunction(func)
  }

  // Insert column name into formula
  const insertColumn = (column: string) => {
    setFormula(prev => prev + column)
  }

  // Insert text at cursor position
  const insertAtCursor = useCallback((text: string) => {
    const input = formulaInputRef.current
    if (input) {
      const start = input.selectionStart || 0
      const end = input.selectionEnd || 0
      const currentFormula = formula

      const newFormula = currentFormula.slice(0, start) + text + currentFormula.slice(end)
      setFormula(newFormula)

      // Set cursor position after inserted text
      setTimeout(() => {
        const newPosition = start + text.length
        input.setSelectionRange(newPosition, newPosition)
        input.focus()
        setCursorPosition(newPosition)
      }, 0)
    } else {
      // Fallback if ref is not available
      setFormula(prev => prev + text)
    }
  }, [formula])

  // Add cell reference to formula (called from table clicks)
  const addCellReference = useCallback((cellRef: string) => {
    insertAtCursor(cellRef)
  }, [insertAtCursor])

  // Add column reference to formula (called from column header clicks)
  const addColumnReference = useCallback((columnRef: string) => {
    insertAtCursor(columnRef)
  }, [insertAtCursor])

  // Handle cursor position changes
  const handleCursorChange = useCallback((e: any) => {
    setCursorPosition(e.target.selectionStart || 0)
  }, [])

  // Handle formula input changes
  const handleFormulaChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormula(e.target.value)
    setCursorPosition(e.target.selectionStart || 0)

    // Real-time validation (optional)
    if (e.target.value.trim()) {
      const errors = validateFormula(e.target.value)
      setValidationErrors(errors)
    } else {
      setValidationErrors([])
    }
  }, [validateFormula])

  // Expose functions to parent component
  useEffect(() => {
    if (onCellClick) {
      // Store the function reference so parent can call it
      (window as any).addCellToFormula = addCellReference
    }
    if (onColumnClick) {
      // Store the function reference so parent can call it
      (window as any).addColumnToFormula = addColumnReference
    }
  }, [addCellReference, addColumnReference, onCellClick, onColumnClick])

  // Copy result to clipboard
  const copyResult = () => {
    if (result?.result !== null && result?.result !== undefined) {
      navigator.clipboard.writeText(result.result.toString())
      toast.success('Result copied to clipboard')
    }
  }

  // Load formula from history
  const loadFromHistory = (historyItem: FormulaHistory) => {
    setFormula(historyItem.formula)
    setResult({
      formula: historyItem.formula,
      result: historyItem.result,
      timestamp: historyItem.timestamp
    })
  }

  // Clear history
  const clearHistory = () => {
    setHistory([])
    toast.success('History cleared')
  }

  // Open save dialog with pre-filled values
  const openSaveDialog = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save. Please calculate a formula first.')
      return
    }

    // Pre-fill the title and description
    const defaultTitle = formula.length > 30
      ? `${formula.substring(0, 30)}...`
      : formula

    const formattedResult = typeof result.result === 'number'
      ? result.result.toLocaleString()
      : result.result

    const defaultDescription = `Result: ${formattedResult}`

    setSaveTitle(defaultTitle)
    setSaveDescription(defaultDescription)
    setShowSaveDialog(true)
  }

  // Save calculator result to dashboard
  const saveToDashboard = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save.')
      return
    }

    if (!saveTitle.trim()) {
      toast.error('Please enter a title for the calculation.')
      return
    }

    // Call the save function from useChartSaving hook with icon
    handleSaveCalculatorResult(
      formula,
      result.result,
      saveTitle.trim(),
      saveDescription.trim() || `Result: ${result.result}`,
      undefined, // resultId (optional)
    )

    // Close dialog and reset form
    setShowSaveDialog(false)
    setSaveTitle('')
    setSaveDescription('')
    setSelectedIcon('Calculator') // Reset to default
  }

  return (
    <div className="w-full max-w-full overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-blue-600 flex-shrink-0" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Excel Calculator</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
        >
          ×
        </Button>
      </div>

      {/* Compact Formula Input */}
      <div className="space-y-3">
        <div className="flex flex-wrap gap-2">
          <div className="flex-1 min-w-[200px]">
            <Input
              ref={formulaInputRef}
              value={formula}
              onChange={handleFormulaChange}
              onSelect={handleCursorChange}
              onKeyUp={handleCursorChange}
              onClick={handleCursorChange}
              placeholder="=SUM(A:A), =AVERAGE(A1:A10), =A1+B1*2, etc."
              className={`font-mono text-sm h-8 bg-white dark:bg-gray-800 w-full ${
                validationErrors.length > 0 ? 'border-red-500' : ''
              }`}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCalculate()
                }
              }}
              disabled={isCalculating}
            />
            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="text-xs text-red-500 mt-1">
                {validationErrors[0]}
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={handleCalculate}
              size="sm"
              className="h-8 px-4 text-xs flex-shrink-0"
              disabled={isCalculating || !formula.trim()}
            >
              {isCalculating ? 'Calculating...' : 'Calculate'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setFormula('')
                setValidationErrors([])
                setResult(null)
                formulaInputRef.current?.focus()
              }}
              className="h-8 px-3 text-xs flex-shrink-0"
            >
              Clear
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('SUM(A:A)')}
            className="h-6 px-2 text-xs"
          >
            SUM(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('AVERAGE(A:A)')}
            className="h-6 px-2 text-xs"
          >
            AVG(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('COUNT(A:A)')}
            className="h-6 px-2 text-xs"
          >
            COUNT(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('IF(A1>0,"Yes","No")')}
            className="h-6 px-2 text-xs"
          >
            IF()
          </Button>
          {['+', '-', '*', '/', '(', ')', ',', '=', '>', '<'].map((op) => (
            <Button
              key={op}
              variant="outline"
              size="sm"
              onClick={() => insertAtCursor(op)}
              className="h-6 w-6 p-0 text-xs"
            >
              {op}
            </Button>
          ))}
        </div>
      </div>

      {/* Result Display */}
      {result && (
        <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xs text-gray-500 mb-1">Result:</div>
              <div className="text-lg font-mono">
                {result.error ? (
                  <span className="text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {result.error}
                  </span>
                ) : (
                  <span className="text-green-600 font-semibold">
                    {typeof result.result === 'number'
                      ? result.result.toLocaleString()
                      : result.result}
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {result.result !== null && !result.error && (
                <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={openSaveDialog}
                      className="h-7 px-2 text-xs"
                      title="Save this calculation result to dashboard"
                    >
                      <Calculator className="h-3 w-3 mr-1" />
                      Save to Dashboard
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Calculator className="h-4 w-4" />
                        Save Calculator Result
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          value={saveTitle}
                          onChange={(e) => setSaveTitle(e.target.value)}
                          placeholder="Enter a title for this calculation"
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Choose Icon</Label>
                        <div className="grid grid-cols-6 gap-2 max-h-32 overflow-y-auto p-2 border rounded-lg">
                          {AVAILABLE_ICONS.map((iconInfo) => {
                            const IconComponent = iconInfo.icon
                            return (
                              <Button
                                key={iconInfo.name}
                                variant={selectedIcon === iconInfo.name ? "default" : "outline"}
                                size="sm"
                                className="h-10 w-10 p-0"
                                onClick={() => setSelectedIcon(iconInfo.name)}
                                type="button"
                              >
                                <IconComponent className={`h-4 w-4 ${iconInfo.color}`} />
                              </Button>
                            )
                          })}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Selected: {AVAILABLE_ICONS.find(i => i.name === selectedIcon)?.name || 'Calculator'}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description">Description (optional)</Label>
                        <Textarea
                          id="description"
                          value={saveDescription}
                          onChange={(e) => setSaveDescription(e.target.value)}
                          placeholder="Add a description..."
                          rows={3}
                          className="w-full resize-none"
                        />
                      </div>
                      <div className="bg-muted p-3 rounded-lg">
                        <div className="text-xs text-muted-foreground mb-1">Formula:</div>
                        <div className="font-mono text-sm">{formula}</div>
                        <div className="text-xs text-muted-foreground mt-2 mb-1">Result:</div>
                        <div className="font-mono text-lg font-semibold text-green-600">
                          {typeof result.result === 'number'
                            ? result.result.toLocaleString()
                            : result.result}
                        </div>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setShowSaveDialog(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={saveToDashboard}
                          disabled={!saveTitle.trim()}
                        >
                          <Calculator className="h-4 w-4 mr-2" />
                          Save to Dashboard
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              {result.result !== null && (
                <Button variant="outline" size="sm" onClick={copyResult} className="h-7 w-7 p-0">
                  <Copy className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Compact Reference Section */}
      <div className="mt-3">
        <Tabs defaultValue="columns" className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="columns" className="text-xs">Columns</TabsTrigger>
            <TabsTrigger value="functions" className="text-xs">Functions</TabsTrigger>
            <TabsTrigger value="history" className="text-xs">History</TabsTrigger>
          </TabsList>

          <TabsContent value="columns" className="mt-2">
            <ScrollArea className="h-24">
              <div className="flex flex-wrap gap-1">
                {columns.map((column, index) => {
                  const excelColumn = String.fromCharCode(65 + (index % 26))
                  return (
                    <Button
                      key={column}
                      variant="outline"
                      size="sm"
                      onClick={() => insertAtCursor(`${excelColumn}:${excelColumn}`)}
                      className="h-6 px-2 text-xs font-mono"
                      title={`${column} (Excel: ${excelColumn}:${excelColumn})`}
                    >
                      {excelColumn}:{excelColumn}
                    </Button>
                  )
                })}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="functions" className="mt-2">
            <ScrollArea className="h-24">
              <div className="flex flex-wrap gap-1">
                {[
                  { func: 'SUM', example: '=SUM(A:A)' },
                  { func: 'AVERAGE', example: '=AVERAGE(A1:A10)' },
                  { func: 'COUNT', example: '=COUNT(A:A)' },
                  { func: 'MIN', example: '=MIN(A:A)' },
                  { func: 'MAX', example: '=MAX(A:A)' },
                  { func: 'IF', example: '=IF(A1>10,"High","Low")' },
                  { func: 'ROUND', example: '=ROUND(A1,2)' },
                  { func: 'AND', example: '=AND(A1>0,B1<100)' },
                  { func: 'OR', example: '=OR(A1>10,B1>20)' }
                ].map(({ func, example }) => (
                  <Button
                    key={func}
                    variant="outline"
                    size="sm"
                    onClick={() => insertAtCursor(example)}
                    className="h-6 px-2 text-xs font-mono"
                    title={`Click to insert: ${example}`}
                  >
                    {func}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="history" className="mt-2">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs text-gray-500">Recent calculations</span>
              {history.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearHistory} className="h-6 w-6 p-0">
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
            <ScrollArea className="h-24">
              <div className="space-y-1">
                {history.length === 0 ? (
                  <div className="text-center text-gray-400 py-4 text-xs">
                    No calculations yet
                  </div>
                ) : (
                  history.slice(0, 3).map((item) => (
                    <div
                      key={item.id}
                      className="p-2 border rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      onClick={() => loadFromHistory(item)}
                    >
                      <div className="font-mono text-xs truncate">{item.formula}</div>
                      <div className="text-xs text-green-600">
                        = {typeof item.result === 'number'
                          ? item.result.toLocaleString()
                          : item.result}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
