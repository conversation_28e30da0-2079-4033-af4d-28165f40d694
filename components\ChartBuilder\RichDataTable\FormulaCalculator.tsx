'use client'

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calculator, AlertCircle, Copy, Trash2, Save, TrendingUp, DollarSign, Users, Target, BarChart3, PieChart, Activity, Zap, Star, Award, Briefcase, Calendar, FileText, Settings, Heart, Shield, Globe, Home, Hash } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory } from './types'
import { useChartSaving } from '../chartbuilderlogic/useChartSaving'

// Available icons for calculator cards
const AVAILABLE_ICONS = [
  { name: 'Calculator', icon: Calculator, color: 'text-blue-600' },
  { name: 'TrendingUp', icon: TrendingUp, color: 'text-green-600' },
  { name: 'DollarSign', icon: DollarSign, color: 'text-emerald-600' },
  { name: 'Users', icon: Users, color: 'text-purple-600' },
  { name: 'Target', icon: Target, color: 'text-red-600' },
  { name: 'BarChart3', icon: BarChart3, color: 'text-indigo-600' },
  { name: 'PieChart', icon: PieChart, color: 'text-pink-600' },
  { name: 'Activity', icon: Activity, color: 'text-orange-600' },
  { name: 'Zap', icon: Zap, color: 'text-yellow-600' },
  { name: 'Star', icon: Star, color: 'text-amber-600' },
  { name: 'Award', icon: Award, color: 'text-cyan-600' },
  { name: 'Briefcase', icon: Briefcase, color: 'text-slate-600' },
  { name: 'Calendar', icon: Calendar, color: 'text-teal-600' },
  { name: 'FileText', icon: FileText, color: 'text-gray-600' },
  { name: 'Settings', icon: Settings, color: 'text-stone-600' },
  { name: 'Heart', icon: Heart, color: 'text-rose-600' },
  { name: 'Shield', icon: Shield, color: 'text-blue-700' },
  { name: 'Globe', icon: Globe, color: 'text-green-700' },
  { name: 'Home', icon: Home, color: 'text-blue-500' },
  { name: 'Hash', icon: Hash, color: 'text-gray-500' }
]

interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
  onCellClick?: (cellRef: string) => void
  onColumnClick?: (columnRef: string) => void
}

export function FormulaCalculator({ data, columns, onClose, onCellClick, onColumnClick }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [isCalculating, setIsCalculating] = useState(false)
  const formulaInputRef = useRef<HTMLInputElement>(null)

  // Save dialog state
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveTitle, setSaveTitle] = useState('')
  const [saveDescription, setSaveDescription] = useState('')
  const [selectedIcon, setSelectedIcon] = useState('Calculator')

  // Use the chart saving hook for dashboard functionality
  const { handleSaveCalculatorResult } = useChartSaving()

  // Helper function to convert number to Excel column name (0->A, 1->B, 25->Z, 26->AA)
  const getExcelColumnName = useCallback((index: number): string => {
    let result = ''
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result
      index = Math.floor(index / 26) - 1
    }
    return result
  }, [])

  // Helper function to convert Excel column name to index (A->0, B->1, AA->26)
  const getColumnIndex = useCallback((columnName: string): number => {
    let result = 0
    for (let i = 0; i < columnName.length; i++) {
      result = result * 26 + (columnName.charCodeAt(i) - 64)
    }
    return result - 1
  }, [])

  // Create Excel-like column mapping (A, B, C, etc.)
  const columnMapping = useMemo(() => {
    const mapping: Record<string, string> = {}
    const reverseMapping: Record<string, string> = {}

    columns.forEach((column, index) => {
      const excelColumn = getExcelColumnName(index)
      mapping[excelColumn] = column
      reverseMapping[column] = excelColumn
    })

    return { mapping, reverseMapping }
  }, [columns, getExcelColumnName])

  // Get cell value by Excel reference (A1, B2, etc.)
  const getCellValue = useCallback((cellRef: string): number => {
    const match = cellRef.match(/^([A-Z]+)(\d+)$/)
    if (!match) return 0

    const columnName = match[1]
    const rowNumber = parseInt(match[2]) - 1 // Convert to 0-based index
    
    const actualColumn = columnMapping.mapping[columnName]
    if (!actualColumn || rowNumber < 0 || rowNumber >= data.length) return 0

    const value = data[rowNumber][actualColumn]
    
    // Handle different value types
    if (value === null || value === undefined) return 0
    if (typeof value === 'number') return value
    if (typeof value === 'string' && !isNaN(Number(value))) return Number(value)
    if (typeof value === 'boolean') return value ? 1 : 0
    return 0
  }, [columnMapping, data])

  // Get range values (A1:A4, B2:D5, etc.)
  const getRangeValues = useCallback((rangeRef: string): number[] => {
    const match = rangeRef.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
    if (!match) return []

    const [_, startCol, startRow, endCol, endRow] = match
    const startColIndex = getColumnIndex(startCol)
    const endColIndex = getColumnIndex(endCol)
    const startRowIndex = parseInt(startRow) - 1
    const endRowIndex = parseInt(endRow) - 1

    // Handle reversed ranges
    const minColIndex = Math.min(startColIndex, endColIndex)
    const maxColIndex = Math.max(startColIndex, endColIndex)
    const minRowIndex = Math.min(startRowIndex, endRowIndex)
    const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)

    const values: number[] = []
    
    for (let row = minRowIndex; row <= maxRowIndex; row++) {
      if (row < 0) continue
      
      for (let col = minColIndex; col <= maxColIndex; col++) {
        if (col < 0 || col >= columns.length) continue
        
        const columnName = getExcelColumnName(col)
        const cellRef = `${columnName}${row + 1}`
        const value = getCellValue(cellRef)
        values.push(value)
      }
    }
    
    return values
  }, [getColumnIndex, getExcelColumnName, getCellValue, data, columns])

  // Excel-like formula evaluation
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    setIsCalculating(true)

    try {
      let processedFormula = formulaText.trim()

      // Remove leading = if present (Excel style)
      if (processedFormula.startsWith('=')) {
        processedFormula = processedFormula.substring(1)
      }

      console.log(`Processing formula: ${processedFormula}`)

      // Process Excel functions with proper range handling
      processedFormula = processedFormula
        // SUM function
        .replace(/SUM\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing SUM: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const sum = values.reduce((acc, val) => acc + val, 0)
            console.log(`SUM(${args}) = ${sum}`)
            return sum.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`SUM(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values (A1,B1,C1)
          if (args.includes(',')) {
            const items = args.split(',').map(item => item.trim())
            let sum = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                sum += getCellValue(item)
              } else if (!isNaN(Number(item))) {
                sum += Number(item)
              }
            }
            console.log(`SUM(${args}) = ${sum}`)
            return sum.toString()
          }
          
          // Handle direct number
          if (!isNaN(Number(args))) {
            return args
          }
          
          return '0'
        })

        // AVERAGE function
        .replace(/AVERAGE\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing AVERAGE: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const avg = values.length > 0 ? values.reduce((acc, val) => acc + val, 0) / values.length : 0
            console.log(`AVERAGE(${args}) = ${avg}`)
            return avg.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`AVERAGE(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map(item => item.trim())
            let sum = 0
            let count = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                sum += getCellValue(item)
                count++
              } else if (!isNaN(Number(item))) {
                sum += Number(item)
                count++
              }
            }
            const avg = count > 0 ? sum / count : 0
            console.log(`AVERAGE(${args}) = ${avg}`)
            return avg.toString()
          }
          
          return args
        })

        // COUNT function
        .replace(/COUNT\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing COUNT: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            console.log(`COUNT(${args}) = ${values.length}`)
            return values.length.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`COUNT(${args}) = 1`)
            return '1'
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map(item => item.trim())
            let count = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                count++
              } else if (!isNaN(Number(item))) {
                count++
              }
            }
            console.log(`COUNT(${args}) = ${count}`)
            return count.toString()
          }
          
          return '1'
        })

        // MIN function
        .replace(/MIN\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MIN: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`MIN(${args}) = ${min}`)
            return min.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`MIN(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map(item => item.trim())
            const values: number[] = []
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                values.push(getCellValue(item))
              } else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`MIN(${args}) = ${min}`)
            return min.toString()
          }
          
          return args
        })

        // MAX function
        .replace(/MAX\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MAX: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`MAX(${args}) = ${max}`)
            return max.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`MAX(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map(item => item.trim())
            const values: number[] = []
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                values.push(getCellValue(item))
              } else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`MAX(${args}) = ${max}`)
            return max.toString()
          }
          
          return args
        })

      // Replace any remaining cell references with values
      processedFormula = processedFormula.replace(/([A-Z]+)(\d+)/g, (match) => {
        const cellValue = getCellValue(match)
        console.log(`Replacing cell ${match} with value: ${cellValue}`)
        return cellValue.toString()
      })

      console.log(`Final formula to evaluate: ${processedFormula}`)

      // If the formula is just a number after processing, return it directly
      if (!isNaN(Number(processedFormula))) {
        const numResult = Number(processedFormula)
        return {
          formula: formulaText,
          result: numResult,
          timestamp: Date.now()
        }
      }

      // Otherwise evaluate with math.js
      const result = math.evaluate(processedFormula)
      
      // Post-process result for Excel compatibility
      let finalResult = result
      if (typeof finalResult === 'number') {
        if (isNaN(finalResult)) finalResult = '#NUM!'
        if (!isFinite(finalResult)) finalResult = '#DIV/0!'
        if (finalResult > 1e15) finalResult = '#NUM!'
        if (finalResult < -1e15) finalResult = '#NUM!'
      }

      return {
        formula: formulaText,
        result: finalResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      console.error('Formula evaluation error:', error)
      return {
        formula: formulaText,
        result: null,
        error: error.message || 'Calculation failed',
        timestamp: Date.now()
      }
    } finally {
      setIsCalculating(false)
    }
  }, [getCellValue, getRangeValues])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    if (calculationResult.result !== null && !calculationResult.error) {
      const historyItem: FormulaHistory = {
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert text at cursor position
  const insertAtCursor = useCallback((text: string) => {
    const input = formulaInputRef.current
    if (input) {
      const start = input.selectionStart || 0
      const end = input.selectionEnd || 0
      const currentFormula = formula
      const newFormula = currentFormula.slice(0, start) + text + currentFormula.slice(end)
      setFormula(newFormula)

      // Set cursor position after inserted text
      setTimeout(() => {
        if (input) {
          input.focus()
          input.setSelectionRange(start + text.length, start + text.length)
        }
      }, 0)
    } else {
      // Fallback if no input ref
      setFormula(prev => prev + text)
    }
  }, [formula])

  // Add cell reference to formula (called from table clicks)
  const addCellReference = useCallback((cellRef: string) => {
    insertAtCursor(cellRef)
  }, [insertAtCursor])

  // Clear formula and result
  const clearFormula = () => {
    setFormula('')
    setResult(null)
  }

  // Copy result to clipboard
  const copyResult = () => {
    if (result && result.result !== null) {
      navigator.clipboard.writeText(String(result.result))
      toast.success('Result copied to clipboard')
    }
  }

  // Open save dialog with pre-filled values
  const openSaveDialog = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save. Please calculate a formula first.')
      return
    }

    // Pre-fill the title and description
    const defaultTitle = formula.length > 30
      ? `${formula.substring(0, 30)}...`
      : formula

    setSaveTitle(defaultTitle)
    setSaveDescription(`Result: ${result.result}`)
    setShowSaveDialog(true)
  }

  // Save calculator result to dashboard
  const saveToDashboard = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save.')
      return
    }

    if (!saveTitle.trim()) {
      toast.error('Please enter a title for the calculation.')
      return
    }

    // Call the save function from useChartSaving hook with icon
    handleSaveCalculatorResult(
      formula,
      result.result,
      saveTitle,
      saveDescription,
      selectedIcon
    )

    setShowSaveDialog(false)
    setSaveTitle('')
    setSaveDescription('')
    setSelectedIcon('Calculator') // Reset to default
  }

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCalculate()
    }
  }

  return (
    <div className="w-full max-w-full overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-blue-600 flex-shrink-0" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Excel Calculator</h3>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
          >
            ×
          </Button>
        )}
      </div>

      {/* Formula Input */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              ref={formulaInputRef}
              value={formula}
              onChange={(e) => setFormula(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter Excel formula (e.g., =SUM(A1:A4), =AVERAGE(B1:B10))"
              className="font-mono text-sm"
            />
          </div>
          <Button
            onClick={handleCalculate}
            disabled={isCalculating || !formula.trim()}
            className="px-4"
          >
            {isCalculating ? 'Calculating...' : 'Calculate'}
          </Button>
        </div>

        {/* Quick Function Buttons */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('SUM(')}
            className="text-xs"
          >
            SUM
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('AVERAGE(')}
            className="text-xs"
          >
            AVERAGE
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('COUNT(')}
            className="text-xs"
          >
            COUNT
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('MIN(')}
            className="text-xs"
          >
            MIN
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('MAX(')}
            className="text-xs"
          >
            MAX
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearFormula}
            className="text-xs"
          >
            Clear
          </Button>
        </div>

        {/* Column References */}
        <div className="flex flex-wrap gap-1">
          <span className="text-xs text-gray-600 mr-2">Columns:</span>
          {columns.slice(0, 10).map((column, index) => {
            const excelColumn = getExcelColumnName(index)
            return (
              <Button
                key={column}
                variant="ghost"
                size="sm"
                onClick={() => insertAtCursor(excelColumn)}
                className="text-xs h-6 px-2"
                title={`${excelColumn} = ${column}`}
              >
                {excelColumn}
              </Button>
            )
          })}
          {columns.length > 10 && (
            <span className="text-xs text-gray-500">+{columns.length - 10} more</span>
          )}
        </div>
      </div>

      {/* Result Display */}
      {result && (
        <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="text-xs text-gray-500 mb-1">Formula: {result.formula}</div>
              {result.error ? (
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Error: {result.error}</span>
                </div>
              ) : (
                <div className="text-lg font-bold text-green-600">
                  {typeof result.result === 'number' ? result.result.toLocaleString() : result.result}
                </div>
              )}
            </div>
            {!result.error && (
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyResult}
                  className="h-8 w-8 p-0"
                  title="Copy result"
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={openSaveDialog}
                  className="h-8 w-8 p-0"
                  title="Save to dashboard"
                >
                  <Save className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* History */}
      {history.length > 0 && (
        <div className="mt-4">
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Calculations</h4>
          <ScrollArea className="h-24">
            <div className="space-y-1">
              {history.map((item, index) => (
                <div
                  key={index}
                  className="text-xs p-2 bg-white dark:bg-gray-800 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                  onClick={() => setFormula(item.formula)}
                >
                  <div className="font-mono text-gray-600 dark:text-gray-400">{item.formula}</div>
                  <div className="font-semibold text-green-600">
                    {typeof item.result === 'number' ? item.result.toLocaleString() : item.result}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Save Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save Calculation Result</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={saveTitle}
                onChange={(e) => setSaveTitle(e.target.value)}
                placeholder="Enter a title for this calculation"
              />
            </div>
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={saveDescription}
                onChange={(e) => setSaveDescription(e.target.value)}
                placeholder="Add a description..."
                rows={2}
              />
            </div>
            <div>
              <Label>Icon</Label>
              <div className="grid grid-cols-5 gap-2 mt-2">
                {AVAILABLE_ICONS.map((iconItem) => {
                  const IconComponent = iconItem.icon
                  return (
                    <Button
                      key={iconItem.name}
                      variant={selectedIcon === iconItem.name ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedIcon(iconItem.name)}
                      className="h-10 w-10 p-0"
                      title={iconItem.name}
                    >
                      <IconComponent className={`h-4 w-4 ${iconItem.color}`} />
                    </Button>
                  )
                })}
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={saveToDashboard}>
                Save to Dashboard
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
