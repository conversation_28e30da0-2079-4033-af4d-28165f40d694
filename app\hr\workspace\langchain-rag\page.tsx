'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import LangChainRagInterface with SSR disabled
const LangChainRagInterface = dynamic(
  () => import('@/components/LangChainRag/LangChainRagInterface'),
  {
    ssr: false,
    loading: () => (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary/60" />
      </div>
    )
  }
);

export default function LangChainRagPage() {
  return (
    <div className="h-full">
      <Suspense fallback={
        <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary/60" />
        </div>
      }>
        <LangChainRagInterface />
      </Suspense>
    </div>
  );
}
