"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useAuth, useUser } from "@clerk/nextjs"
import { ChevronRight, LucideIcon } from "lucide-react"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { TeamSwitcher } from "./team-switch"
import { NavProjects } from "./nav-project"
import { NavUser } from "./nav-user"
import { DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { getRecentNotifications } from "@/actions/chatActions"

// Import icons as React components
import { LayoutDashboard, LineChart, Table, Brain, MessageSquare, Bell, ChartBarIcon, Database } from "lucide-react"

// Notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  time: Date;
  channelId: string;
}

// Route data with collapsible sections
const mainRoutes = [
  {
    title: "Workspace",
    url: "/hr/workspace",
    icon: LayoutDashboard,
    collapsible: false
  },
  {
    title: "HR Dashboard",
    url: "/hr/dashboard",
    icon: LineChart,
    collapsible: false
  },
  {
    title: "Data Analytics",
    icon: Database,
    collapsible: true,
    items: [
      {
        title: "Data Editor",
        url: "/hr/dataeditor",
      },
      {
        title: "Chart Builder",
        url: "/hr/chartbuilder",
      }
    ]
  },
  {
    title: "AI Tools",
    icon: Brain,
    collapsible: true,
    items: [
      {
        title: "LangChain RAG",
        url: "/hr/workspace/langchain-rag",
      },
      {
        title: "AgentSet RAG",
        url: "/agentset-rag",
      }
    ]
  }
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [notifications, setNotifications] = React.useState<Notification[]>([])
  const { isLoaded, isSignedIn } = useAuth()
  const { user } = useUser()
  const pathname = usePathname()
  const router = useRouter()

  // User data from Clerk
  const userData = React.useMemo(() => {
    return {
      name: user?.firstName || user?.username || "User",
      email: user?.emailAddresses[0]?.emailAddress || "<EMAIL>",
      avatar: user?.imageUrl || "/avatars/placeholder.jpg",
    }
  }, [user])

  // Fetch notifications on mount
  React.useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const result = await getRecentNotifications()
        if (result.success) {
          setNotifications(result.notifications)
        }
      } catch (error) {
        console.error("Failed to fetch notifications:", error)
      }
    }

    if (isSignedIn) {
      fetchNotifications()
      // Refresh notifications every minute
      const interval = setInterval(fetchNotifications, 60000)
      return () => clearInterval(interval)
    }
  }, [isSignedIn])

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    router.push(`/hr/workspace/chats/${notification.channelId}`)
    // Mark notification as read by removing it from the list
    setNotifications(prev => prev.filter(n => n.id !== notification.id))
    toast.success("Navigating to message")
  }

  // Add notifications to projects section as a special item
  const projects = [
    {
      name: "Notifications",
      url: "#",
      icon: Bell,
      badge: notifications.length > 0 ? notifications.length : undefined,
      onClick: () => {}, // Empty function because we're using dropdown
      dropdown: notifications.length === 0 ? (
        <DropdownMenuItem>
          <div className="text-sm text-muted-foreground">
            No new messages
          </div>
        </DropdownMenuItem>
      ) : (
        <>
          {notifications.map((notification) => (
            <DropdownMenuItem
              key={notification.id}
              onClick={() => handleNotificationClick(notification)}
              className="cursor-pointer"
            >
              <div className="flex flex-col space-y-1">
                <span className="font-medium">{notification.title}</span>
                <span className="text-sm text-muted-foreground">
                  {notification.message}
                </span>
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(notification.time), { addSuffix: true })}
                </span>
              </div>
            </DropdownMenuItem>
          ))}
        </>
      )
    }
  ]

  // Check if a route or its subitems are active
  const isRouteActive = (route: any) => {
    if (!route.collapsible) {
      return pathname === route.url
    }

    return route.items?.some((item: any) => pathname === item.url) || false
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {mainRoutes.map((route) => {
            const isActive = isRouteActive(route)

            if (!route.collapsible) {
              return (
                <SidebarMenuItem key={route.url || route.title}>
                  {route.url ? (
                    <Link href={route.url}>
                      <SidebarMenuButton className={isActive ? "bg-sidebar-accent" : ""}>
                        {route.icon && <route.icon className="size-4 shrink-0" />}
                        <span>{route.title}</span>
                      </SidebarMenuButton>
                    </Link>
                  ) : (
                    <SidebarMenuButton className={isActive ? "bg-sidebar-accent" : ""}>
                      {route.icon && <route.icon className="size-4 shrink-0" />}
                      <span>{route.title}</span>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              )
            }

            return (
              <Collapsible
                key={route.title}
                asChild
                defaultOpen={isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton className={isActive ? "bg-sidebar-accent" : ""}>
                      {route.icon && <route.icon className="size-4 shrink-0" />}
                      <span>{route.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {route.items?.map((item) => (
                        <SidebarMenuSubItem key={item.url}>
                          <Link href={item.url}>
                            <SidebarMenuSubButton
                              className={pathname === item.url ? "bg-sidebar-accent/50" : ""}
                            >
                              <span>{item.title}</span>
                            </SidebarMenuSubButton>
                          </Link>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            )
          })}
        </SidebarMenu>

        <NavProjects projects={projects} />
      </SidebarContent>
      <SidebarFooter>
        {!isLoaded ? (
          <div className="animate-spin rounded-full h-8 w-5 border-b-2 border-primary mx-auto"></div>
        ) : (
          <NavUser user={userData} isSignedIn={isSignedIn} />
        )}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
