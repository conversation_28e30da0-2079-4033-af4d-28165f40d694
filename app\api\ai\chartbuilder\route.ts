import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { prompt, language, datasets } = await req.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    // Create dataset context
    const datasetContext = datasets?.map((ds: any) => {
      const safeName = ds.name
        .replace(/\.[^/.]+$/, '') // Remove file extension
        .replace(/[^a-zA-Z0-9_]/g, '_') // Replace special chars
        .toLowerCase();
      
      return `Dataset: ${safeName}
Table/Variable Name: ${safeName}
Columns: ${ds.columns?.join(', ') || 'Unknown'}
Rows: ${ds.rowCount || ds.data?.length || 'Unknown'}
Sample Data: ${JSON.stringify(ds.sampleData || ds.data?.slice(0, 2) || [], null, 2)}`;
    }).join('\n\n') || 'No datasets provided';

    // Create language-specific system prompt
    const getLanguageInstructions = (lang: string) => {
      switch (lang) {
        case 'sql':
          return `Generate SQL code using these guidelines:
- Use actual dataset names as table names (e.g., employees, sales, etc.)
- Write standard SQL that works with most databases
- Include helpful comments
- Use proper SQL formatting and indentation
- For joins, use meaningful aliases
- Always end statements with semicolons`;

        case 'python':
          return `Generate Python code using these guidelines:
- Datasets are available as variables with their actual names (e.g., employees, sales)
- Also available as df (first dataset), df1, df2, etc.
- Use pandas, numpy, matplotlib, seaborn as needed
- Include helpful comments
- Use proper Python formatting
- For visualizations, use matplotlib or seaborn
- Always assign results to 'result' variable for display`;

        case 'javascript':
          return `Generate JavaScript code using these guidelines:
- Use modern ES6+ syntax
- Include helpful comments
- Use proper formatting and indentation
- Work with the provided dataset objects`;

        default:
          return `Generate ${lang} code with proper formatting and helpful comments.`;
      }
    };

    const systemPrompt = `You are an expert data analyst and programmer. Generate clean, efficient ${language.toUpperCase()} code based on the user's request.

AVAILABLE DATASETS:
${datasetContext}

LANGUAGE-SPECIFIC INSTRUCTIONS:
${getLanguageInstructions(language)}

IMPORTANT RULES:
1. Use actual dataset names (not generic dataset1, dataset2)
2. Generate ONLY the code, no explanations before or after
3. Include brief comments within the code
4. Make the code practical and executable
5. For SQL: Use the actual table names shown above
6. For Python: Use the actual variable names shown above
7. Ensure code is production-ready and follows best practices

USER REQUEST: ${prompt}

Generate the ${language.toUpperCase()} code:`;

    const result = await model.generateContent(systemPrompt);
    const response = await result.response;
    let text = response.text();

    // Clean up the response to extract just the code
    text = text.trim();
    
    // Remove markdown code blocks if present
    const codeBlockRegex = new RegExp(`\`\`\`${language}\\n([\\s\\S]*?)\`\`\``, 'i');
    const match = text.match(codeBlockRegex);
    
    if (match) {
      text = match[1].trim();
    } else {
      // Try general code block pattern
      const generalCodeRegex = /```[\w]*\n([\s\S]*?)```/;
      const generalMatch = text.match(generalCodeRegex);
      if (generalMatch) {
        text = generalMatch[1].trim();
      }
    }

    // Remove any remaining markdown or explanatory text
    const lines = text.split('\n');
    const codeLines = lines.filter(line => {
      const trimmed = line.trim();
      // Keep lines that look like code (not explanatory text)
      return !trimmed.startsWith('Here') && 
             !trimmed.startsWith('This') && 
             !trimmed.startsWith('The above') &&
             !trimmed.startsWith('Note:') &&
             !trimmed.startsWith('Explanation:');
    });

    const cleanedCode = codeLines.join('\n').trim();

    return NextResponse.json({
      success: true,
      code: cleanedCode,
      language: language
    });

  } catch (error) {
    console.error('Gemini API error:', error);
    return NextResponse.json({
      error: 'Failed to generate code. Please try again.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
