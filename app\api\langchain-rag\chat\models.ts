// Model mapping
export const TOGETHER_MODELS: Record<string, string> = {
  'llama-3-70b': 'meta-llama/Llama-3-70b-chat-hf',
  'llama-3-8b': 'meta-llama/Llama-3-8b-chat-hf',
  'mixtral-8x7b': 'mistralai/Mixtral-8x7B-Instruct-v0.1',
  'mistral-7b': 'mistralai/Mistral-7B-Instruct-v0.2',
  'qwen-72b': 'Qwen/Qwen-72B-Chat',
  'meta': 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
};

export const COHERE_MODELS: Record<string, string> = {
  'command-r': 'command-r',
  'command-r-plus': 'command-r-plus'
};

// Helper function to determine model provider
export function getModelProvider(model: string): 'cohere' | 'together' {
  if (Object.keys(COHERE_MODELS).includes(model)) {
    return 'cohere';
  }
  return 'together';
}

// Helper function to get actual model ID
export function getModelId(model: string, provider: 'cohere' | 'together'): string {
  if (provider === 'cohere') {
    return COHERE_MODELS[model] || 'command-r-plus';
  } else {
    return TOGETHER_MODELS[model] || 'meta-llama/Llama-3-70b-chat-hf';
  }
}
