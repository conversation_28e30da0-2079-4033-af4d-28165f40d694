'use client'

import { useState, useCallback } from 'react'
import { toast } from 'sonner'
import { CellData } from './types'
import { Workspace } from '../types/workspace'

export function useWorkspaceManagement() {
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoadingCells, setIsLoadingCells] = useState(false)

  const handleWorkspaceChange = useCallback((workspace: Workspace, setCells?: (cells: CellData[]) => void) => {
    setCurrentWorkspace(workspace)

    // Load cells from the first notebook if it exists and setCells is provided
    if (setCells && workspace.notebooks && workspace.notebooks.length > 0) {
      const firstNotebook = workspace.notebooks[0]
      console.log('Loading cells from notebook:', firstNotebook.name)

      setIsLoadingCells(true)
      fetch(`/api/workspaces/${workspace.id}/notebooks/${firstNotebook.id}/cells`)
        .then(response => response.json())
        .then(data => {
          if (data.success && data.cells) {
            console.log('Loaded cells:', data.cells.length)
            // Convert API cells to CellData format
            const loadedCells: CellData[] = data.cells.map((cell: any) => ({
              id: cell.id,
              content: cell.content,
              language: cell.language,
              cellType: cell.cellType,
              selectedDatasetIds: cell.selectedDatasetIds || [],
              result: cell.result,
              error: cell.error,
              errorDetails: cell.errorDetails,
              executionTime: cell.executionTime,
              isSuccess: cell.isSuccess || false,
              notes: cell.notes
            }))
            setCells(loadedCells)
            toast.success(`Switched to data workspace: ${workspace.name} (${loadedCells.length} cells loaded)`)
          } else {
            toast.success(`Switched to data workspace: ${workspace.name}`)
          }
        })
        .catch(error => {
          console.error('Error loading cells:', error)
          toast.success(`Switched to data workspace: ${workspace.name}`)
        })
        .finally(() => {
          setIsLoadingCells(false)
        })
    } else {
      toast.success(`Switched to data workspace: ${workspace.name}`)
    }
  }, [])

  const saveCurrentStateToWorkspace = useCallback(async (
    cells: CellData[],
    dashboardItems: any[]
  ) => {
    if (!currentWorkspace) {
      toast.error('No data workspace selected')
      return
    }

    console.log('Saving to workspace:', currentWorkspace.name)
    console.log('Cells to save:', cells.length, cells)
    console.log('Dashboard items to save:', dashboardItems.length)

    setIsSaving(true)
    try {
      // Get the main notebook (first one or create if none exists)
      let targetNotebook = currentWorkspace.notebooks[0]
      
      if (!targetNotebook) {
        // Create a default notebook if none exists
        const notebookResponse = await fetch(`/api/workspaces/${currentWorkspace.id}/notebooks`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Analysis Notebook',
            description: 'Default notebook for data analysis'
          }),
        })

        if (!notebookResponse.ok) {
          throw new Error('Failed to create notebook')
        }

        const notebookData = await notebookResponse.json()
        if (!notebookData.success) {
          throw new Error(notebookData.error || 'Failed to create notebook')
        }

        targetNotebook = notebookData.notebook
      }

      // Save cells to the notebook
      const cellPromises = cells.map(async (cell, index) => {
        console.log(`Processing cell ${index + 1}:`, { id: cell.id, content: cell.content?.substring(0, 50) + '...', language: cell.language })

        // Check if cell already exists in the notebook by ID first, then by content
        // @ts-ignore
        const existingCell = targetNotebook.cells.find(c =>
          (cell.id && c.id === cell.id) ||
          (c.content === cell.content && c.language === cell.language)
        )

        if (existingCell) {
          console.log('Updating existing cell:', existingCell.id)
          // Update existing cell
          return fetch(`/api/workspaces/${currentWorkspace.id}/notebooks/${targetNotebook.id}/cells/${existingCell.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: cell.content,
              language: cell.language,
              cellType: cell.cellType || 'code',
              selectedDatasetIds: cell.selectedDatasetIds || [],
              result: cell.result,
              error: cell.error,
              errorDetails: cell.errorDetails,
              executionTime: cell.executionTime,
              isSuccess: cell.isSuccess || false,
              notes: cell.notes
            }),
          })
        } else {
          // Create new cell
          console.log('Creating new cell')
          return fetch(`/api/workspaces/${currentWorkspace.id}/notebooks/${targetNotebook.id}/cells`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              cellType: cell.cellType || 'code',
              language: cell.language,
              content: cell.content,
              selectedDatasetIds: cell.selectedDatasetIds || [],
              result: cell.result,
              error: cell.error,
              errorDetails: cell.errorDetails,
              executionTime: cell.executionTime,
              isSuccess: cell.isSuccess || false,
              notes: cell.notes
            }),
          })
        }
      })

      const cellResults = await Promise.all(cellPromises)
      console.log('Cell save results:', cellResults.map(r => r.status))

      // Save dashboard items if any
      if (dashboardItems.length > 0) {
        let targetDashboard = currentWorkspace.dashboards[0]
        
        if (!targetDashboard) {
          // Create a default dashboard if none exists
          const dashboardResponse = await fetch(`/api/workspaces/${currentWorkspace.id}/dashboards`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: 'Analytics Dashboard',
              description: 'Default dashboard for data visualizations'
            }),
          })

          if (!dashboardResponse.ok) {
            throw new Error('Failed to create dashboard')
          }

          const dashboardData = await dashboardResponse.json()
          if (!dashboardData.success) {
            throw new Error(dashboardData.error || 'Failed to create dashboard')
          }

          targetDashboard = dashboardData.dashboard
        }

        // Save dashboard items
        const itemPromises = dashboardItems.map(async (item) => {
          return fetch(`/api/workspaces/${currentWorkspace.id}/dashboards/${targetDashboard.id}/items`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: item.type,
              title: item.title,
              description: item.description,
              data: item.data,
              config: item.config,
              content: item.content,
              gridColumn: item.gridColumn || 1,
              gridRow: item.gridRow || 1,
              width: item.width || 1,
              height: item.height || 1,
              sourceNotebookId: targetNotebook.id
            }),
          })
        })

        await Promise.all(itemPromises)
      }

      toast.success(`Saved to data workspace: ${currentWorkspace.name}`)
      
      // Refresh workspace data
      const workspaceResponse = await fetch(`/api/workspaces/${currentWorkspace.id}`)
      if (workspaceResponse.ok) {
        const workspaceData = await workspaceResponse.json()
        if (workspaceData.success) {
          setCurrentWorkspace(workspaceData.workspace)
        }
      }

    } catch (error) {
      console.error('Error saving to workspace:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save to data workspace')
    } finally {
      setIsSaving(false)
    }
  }, [currentWorkspace])

  const loadWorkspaceState = useCallback(async (workspaceId: string, setCells?: (cells: CellData[]) => void) => {
    try {
      setIsLoadingCells(true)
      const response = await fetch(`/api/workspaces/${workspaceId}`)

      if (!response.ok) {
        throw new Error('Failed to load workspace')
      }

      const data = await response.json()
      if (data.success) {
        setCurrentWorkspace(data.workspace)

        // Load cells from the first notebook if it exists and setCells is provided
        if (setCells && data.workspace.notebooks && data.workspace.notebooks.length > 0) {
          const firstNotebook = data.workspace.notebooks[0]
          console.log('Loading cells from notebook:', firstNotebook.name)

          try {
            const cellsResponse = await fetch(`/api/workspaces/${workspaceId}/notebooks/${firstNotebook.id}/cells`)
            if (cellsResponse.ok) {
              const cellsData = await cellsResponse.json()
              if (cellsData.success && cellsData.cells) {
                console.log('Loaded cells:', cellsData.cells.length)
                // Convert API cells to CellData format
                const loadedCells: CellData[] = cellsData.cells.map((cell: any) => ({
                  id: cell.id,
                  content: cell.content,
                  language: cell.language,
                  cellType: cell.cellType,
                  selectedDatasetIds: cell.selectedDatasetIds || [],
                  result: cell.result,
                  error: cell.error,
                  errorDetails: cell.errorDetails,
                  executionTime: cell.executionTime,
                  isSuccess: cell.isSuccess || false,
                  notes: cell.notes
                }))
                setCells(loadedCells)
                toast.success(`Loaded ${loadedCells.length} cells from workspace`)
              }
            }
          } catch (cellError) {
            console.error('Error loading cells:', cellError)
            // Don't show error toast for cells, just log it
          }
        }

        return data.workspace
      } else {
        throw new Error(data.error || 'Failed to load workspace')
      }
    } catch (error) {
      console.error('Error loading workspace:', error)
      toast.error('Failed to load workspace')
      return null
    } finally {
      setIsLoadingCells(false)
    }
  }, [])

  return {
    currentWorkspace,
    isSaving,
    isLoadingCells,
    handleWorkspaceChange,
    saveCurrentStateToWorkspace,
    loadWorkspaceState
  }
}
