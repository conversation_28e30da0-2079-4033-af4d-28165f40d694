
import { useState, useEffect, useCallback } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { MessageSquare, Trash2, ArrowUpRight, Check, FileText, Image as ImageIcon, FileSpreadsheet, Download, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { ChatMessage } from "@/types/index";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import Image from "next/image";

interface MessageProps {
  message: ChatMessage;
  channelId: string;
  isCurrentUser: boolean;
  userColors: Record<string, string>;
  onDelete: (messageId: string) => void;
  onReply: (messageId: string) => void;
  onReaction: (messageId: string, emoji: string) => void;
  replyTo?: {
    id: string;
    content: string;
    user: string;
  };
  readBy?: string[];
  fileUrl?: string;
  fileType?: string;
}

export function Message({ 
  message, 
  channelId,
  isCurrentUser, 
  userColors, 
  onDelete, 
  onReply, 
  onReaction,
  replyTo,
  readBy = [],
}: MessageProps) {
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [previewError, setPreviewError] = useState(false);

  const fetchAndCreateBlob = useCallback(async (url: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      setBlobUrl(blobUrl);
      return blobUrl;
    } catch (error) {
      console.error('Error creating blob URL:', error);
      setPreviewError(true);
      return null;
    }
  }, []);

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  const userName = message.user || 'Anonymous';
  const userInitials = userName.substring(0, 2).toUpperCase();

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'sent just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `sent ${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `sent ${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `sent ${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return `sent on ${date.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      })}`;
    }
  };
  const UPLOADTHING_URL = "https://uploadthing.com/f/";
  

  const renderFilePreview = (message: ChatMessage) => {
    const fileUrl = message.fileUrl || (message.fileKey ? `${UPLOADTHING_URL}${message.fileKey}` : null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      if (fileUrl) {
        fetchAndCreateBlob(fileUrl);
        setLoading(false);
      }
    }, [fileUrl]);

    if (!fileUrl || loading) {
      return (
        <Card className="mt-2 overflow-hidden bg-background/50">
          <div className="p-4 flex items-center gap-3">
            <div className="animate-pulse flex items-center gap-3 w-full">
              <div className="h-8 w-8 bg-muted rounded" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-1/3" />
                <div className="h-3 bg-muted rounded w-1/4" />
              </div>
            </div>
          </div>
        </Card>
      );
    }

    const fileName = message.fileName || fileUrl.split('/').pop() || 'File';

    const renderImagePreview = () => (
      <div className="relative">
        <div className="relative w-full h-48 cursor-pointer overflow-hidden">
          {previewError ? (
            // Fallback to direct URL if blob fails
            <Image
              src={fileUrl}
              alt={fileName}
              fill
              style={{ objectFit: 'contain' }}
              className="hover:opacity-90 transition-opacity rounded-md"
              unoptimized
              onError={() => setPreviewError(true)}
            />
          ) : (
            <Image
              src={blobUrl || fileUrl}
              alt={fileName}
              fill
              style={{ objectFit: 'contain' }}
              className="hover:opacity-90 transition-opacity rounded-md"
              unoptimized
              onClick={() => setShowImagePreview(true)}
            />
          )}
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/50 text-white">
            <p className="text-xs truncate">{fileName}</p>
          </div>
        </div>

        <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>
          <DialogContent className="max-w-4xl h-[80vh] p-0">
            <div className="relative w-full h-full bg-black/90">
              <Image
                src={blobUrl || fileUrl}
                alt={fileName}
                fill
                style={{ objectFit: 'contain' }}
                unoptimized
                quality={100}
                priority
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 bg-black/50 hover:bg-black/70"
                onClick={() => setShowImagePreview(false)}
              >
                <X className="h-4 w-4 text-white" />
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );

    const renderPdfPreview = () => (
      <div className="relative">
        <div className="p-4 flex items-center gap-3">
          <FileText className="h-8 w-8 text-red-500" />
          <div className="flex-1">
            <p className="font-medium text-sm">{fileName}</p>
            <p className="text-xs text-muted-foreground">PDF Document</p>
            <div className="mt-2">
              {/* Try multiple PDF preview options */}
              <div className="w-full h-32 rounded border overflow-hidden">
                {blobUrl ? (
                  <object
                    data={blobUrl}
                    type="application/pdf"
                    className="w-full h-full"
                  >
                    <iframe
                      src={`https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`}
                      className="w-full h-full"
                      title={fileName}
                    />
                  </object>
                ) : (
                  <iframe
                    src={`https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`}
                    className="w-full h-full"
                    title={fileName}
                  />
                )}
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open(blobUrl || fileUrl, '_blank')}
          >
            Open
          </Button>
        </div>
      </div>
    );

    return (
      <Card className="mt-2 overflow-hidden bg-background/50">
        {previewError ? (
          // Fallback for any preview error
          <div className="p-4 flex items-center gap-3">
            <FileText className="h-8 w-8 text-muted-foreground" />
            <div className="flex-1">
              <p className="font-medium text-sm">{fileName}</p>
              <p className="text-xs text-muted-foreground">Click to view</p>
            </div>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => window.open(fileUrl, '_blank')}
            >
              <Download className="h-4 w-4 mr-2" />
              Open
            </Button>
          </div>
        ) : (
          // Attempt to render preview
          <>
            {renderImagePreview()}
            {renderPdfPreview()}
          </>
        )}
      </Card>
    );
  };

  // Add this component for text preview
  const TextPreview = ({ url }: { url: string }) => {
    const [content, setContent] = useState<string>('Loading...');

    useEffect(() => {
      fetch(url)
        .then(response => response.text())
        .then(text => setContent(text.slice(0, 500) + (text.length > 500 ? '...' : '')))
        .catch(() => setContent('Failed to load preview'));
    }, [url]);

    return <pre className="whitespace-pre-wrap">{content}</pre>;
  };

  return (
    <div className={cn(
      "w-full px-2 py-1 hover:bg-muted/50 transition-colors relative group",
      replyTo && "ml-4"
    )}>
      {replyTo && (
        <>
          <div className="absolute left-0 top-0 bottom-0 w-[2px] bg-primary/20" />
          <div className="absolute left-[2px] top-4 w-3 h-[2px] bg-primary/20" />
        </>
      )}
      
      <div className={cn(
        "flex items-start gap-2 max-w-2xl mx-auto",
        isCurrentUser ? "flex-row-reverse" : "flex-row"
      )}>
        <Avatar className="h-6 w-6 flex-shrink-0">
          <AvatarImage src={message.avatar} alt={userName} />
          <AvatarFallback className={cn(
            "text-[10px] font-medium",
            isCurrentUser ? userColors.owner : userColors.member
          )}>
            {userInitials}
          </AvatarFallback>
        </Avatar>

        <div className={cn(
          "flex-1 min-w-0 space-y-1 relative group",
          isCurrentUser ? "items-end" : "items-start"
        )}>
          {replyTo && (
            <div className={cn(
              "flex flex-col gap-1.5 mb-2",
              isCurrentUser ? "items-end" : "items-start"
            )}>
              <div className="flex items-center gap-2 text-xs">
                <div className="flex items-center gap-1.5 px-2 py-1 rounded-md bg-muted/50">
                  <ArrowUpRight className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">Replying to</span>
                  <span className="font-medium text-blue-500">{replyTo.user}</span>
                  <span className="text-muted-foreground truncate max-w-[200px]">
                    {replyTo.content}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className={cn(
            "flex items-center gap-2",
            isCurrentUser ? "justify-end" : "justify-start"
          )}>
            <span className="text-xs font-medium truncate">{userName}</span>
            <span className="text-[10px] text-muted-foreground">
              {formatMessageTime(message.timestamp)}
            </span>
          </div>

          <div className="relative flex items-start gap-2">
            <div className={cn(
              "rounded-lg px-3 py-1.5 text-sm break-words",
              isCurrentUser 
                ? "bg-primary text-primary-foreground rounded-tr-none ml-auto" 
                : "bg-muted text-foreground rounded-tl-none",
              "max-w-[85%]"
            )}>
              {message.content && (
                <div className="mb-2">{message.content}</div>
              )}

              {(message.fileKey || message.fileUrl) && (
                <div className={cn(
                  "mt-2",
                  message.content ? "border-t border-primary/20 pt-2" : ""
                )}>
                  {renderFilePreview(message)}
                </div>
              )}
            </div>

            <div className={cn(
              "opacity-0 group-hover:opacity-100 transition-opacity",
              isCurrentUser ? "order-first" : "order-last"
            )}>
              {isCurrentUser ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-destructive hover:bg-destructive/10 hover:text-destructive"
                  onClick={() => onDelete(message.id)}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => onReply(message.id)}
                >
                  <MessageSquare className="h-3.5 w-3.5" />
                </Button>
              )}
            </div>
          </div>

          {message.reactions?.length > 0 && (
            <div className={cn(
              "flex items-center gap-1.5",
              isCurrentUser ? "justify-end" : "justify-start"
            )}>
              {message.reactions.map((reaction: any, index: number) => (
                <button
                  key={index}
                  onClick={() => onReaction(message.id, reaction.emoji)}
                  className="flex items-center gap-1 px-1.5 py-0.5 rounded text-[10px] bg-muted hover:bg-muted/80"
                >
                  <span>{reaction.emoji}</span>
                  <span>{reaction.count}</span>
                </button>
              ))}
            </div>
          )}

          {isCurrentUser && (
            <div className={cn(
              "flex items-center gap-0.5 absolute -bottom-4 right-0",
              "text-xs text-muted-foreground"
            )}>
              {readBy.length > 0 ? (
                <div className="flex -space-x-1">
                  {readBy.map((reader, i) => (
                    <Check 
                      key={i} 
                      className={cn(
                        "h-3 w-3",
                        i === 0 ? "text-blue-500" : "text-muted-foreground/50"
                      )} 
                    />
                  ))}
                </div>
              ) : (
                <Check className="h-3 w-3 text-muted-foreground/50" />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
