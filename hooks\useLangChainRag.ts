import { useState, useEffect } from 'react';
import { toast } from 'sonner';


export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  id?: any
  sources?:any
  tokenUsage?: {
    totalTokens: number;
    promptTokens?: number;
    completionTokens?: number;
  };
}


// Define types for LangChain RAG
export type LangChainChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string; // Must be a string
  deepResearch?: {
    iterations: any[];
    totalIterations: number;
    subQuestions: string[];
  };
};

export type LangChainModel =
  // Cohere models
  'command-r' | 'command-r-plus' |
  // Together AI models
  'llama-3-70b' | 'llama-3-8b' | 'mixtral-8x7b' | 'mistral-7b' | 'qwen-72b' | 'meta';

export type PineconeStatus = {
  isInitialized: boolean;
  recordCount: number;
  namespace: string;
};

// Hook for interacting with LangChain RAG
export const useLangChainRag = () => {
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>([]);
  const [selectedPDFs, setSelectedPDFs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEmbedding, setIsEmbedding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<LangChainChatMessage[]>([]);
  const [selectedModel, setSelectedModel] = useState<LangChainModel>('command-r-plus');
  const [sourceDocuments, setSourceDocuments] = useState<any[]>([]);
  const [useDeepResearch, setUseDeepResearch] = useState<boolean>(true); // Enable by default for best experience
  const [maxIterations, setMaxIterations] = useState<number>(4); // Increase default for better results
  const [currentIterations, setCurrentIterations] = useState<number>(0);
  const [searchStrategy, setSearchStrategy] = useState<'hybrid' | 'semantic' | 'keyword'>('hybrid');
  const [retrievalK, setRetrievalK] = useState<number>(15); // Increase for large datasets
  const [pineconeStatus, setPineconeStatus] = useState<PineconeStatus>({
    isInitialized: false,
    recordCount: 0,
    namespace: 'adeloop'
  });

  // Check Pinecone status on mount
  useEffect(() => {
    checkPineconeStatus();
  }, []);

  // Check Pinecone status
  const checkPineconeStatus = async () => {
    try {
      const response = await fetch('/api/langchain-rag/pinecone', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to check Pinecone status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setPineconeStatus(data);
      }
    } catch (err: any) {
      console.error('Error checking Pinecone status:', err);
    }
  };

  // Embed dataset in Pinecone
  const embedDataset = async (datasetId: string) => {
    if (!datasetId) {
      setError('No dataset selected');
      return false;
    }

    setIsEmbedding(true);
    setError(null);

    try {
      const response = await fetch('/api/langchain-rag/embed', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          datasetId
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to embed dataset: ${response.status}`);
      }

      const data = await response.json();
      console.log('Embed response:', data);
      if (data.success) {
        toast.success('Dataset embedded successfully');
        checkPineconeStatus(); // Refresh status after embedding
        return true;
      } else {
        throw new Error(data.error || 'Failed to embed dataset');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to embed dataset');
      console.error('Error embedding dataset:', err);
      toast.error(`Error embedding dataset: ${err.message}`);
      return false;
    } finally {
      setIsEmbedding(false);
    }
  };

  // Embed PDF in Pinecone
  const embedPDF = async (pdfId: string, file: File) => {
    if (!pdfId) {
      setError('No PDF selected');
      return false;
    }

    if (!file) {
      setError('No PDF file provided');
      return false;
    }

    setIsEmbedding(true);
    setError(null);

    try {
      // Create form data to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('pdfId', pdfId);

      const response = await fetch('/api/langchain-rag/embed-pdf', {
        method: 'POST',
        credentials: 'include',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Failed to embed PDF: ${response.status}`);
      }

      const data = await response.json();
      console.log('Embed PDF response:', data);
      if (data.success) {
        toast.success('PDF embedded successfully');
        checkPineconeStatus(); // Refresh status after embedding
        return true;
      } else {
        throw new Error(data.error || 'Failed to embed PDF');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to embed PDF');
      console.error('Error embedding PDF:', err);
      toast.error(`Error embedding PDF: ${err.message}`);
      return false;
    } finally {
      setIsEmbedding(false);
    }
  };

  // Add a message to the chat
  const addMessage = (message: LangChainChatMessage) => {
    setChatMessages(prev => [...prev, message]);
  };

  // Clear chat messages
  const clearChat = () => {
    setChatMessages([]);
  };

  // Fetch datasets from the API
  const fetchDatasets = async () => {
    try {
      const response = await fetch('/api/datasets', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      return data.datasets || [];
    } catch (err) {
      console.error('Error fetching datasets:', err);
      return [];
    }
  };

  // Fetch PDFs from the API
  const fetchPDFs = async () => {
    try {
      const response = await fetch('/api/pdf-documents', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch PDFs: ${response.status}`);
      }

      const data = await response.json();
      return data.documents || [];
    } catch (err) {
      console.error('Error fetching PDFs:', err);
      return [];
    }
  };

  // Delete embeddings from Pinecone
  const deleteEmbedding = async (type: 'dataset' | 'pdf', id: string) => {
    if (!id) {
      setError(`No ${type} selected`);
      return false;
    }

    try {
      const response = await fetch('/api/langchain-rag/delete-embedding', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          datasetId: type === 'dataset' ? id : undefined,
          pdfId: type === 'pdf' ? id : undefined,
          namespace: pineconeStatus.namespace
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to delete embeddings: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        toast.success(`${type === 'dataset' ? 'Dataset' : 'PDF'} embeddings deleted successfully`);
        checkPineconeStatus(); // Refresh status after deletion
        return true;
      } else {
        throw new Error(data.error || `Failed to delete ${type} embeddings`);
      }
    } catch (err: any) {
      setError(err.message || `Failed to delete ${type} embeddings`);
      console.error(`Error deleting ${type} embeddings:`, err);
      toast.error(`Error deleting embeddings: ${err.message}`);
      return false;
    }
  };

  // Send a message and get a response using RAG (internal implementation)
  // This function is not exported directly, but used by handleSendMessage
  const _sendMessageInternal = async (
    message: string,
    deepResearch: boolean = false
  ): Promise<Message> => {
    try {
      // Validate that at least one dataset or PDF is selected
      if (selectedDatasets.length === 0 && selectedPDFs.length === 0) {
        throw new Error('Please select and embed at least one dataset or PDF document before sending a message.');
      }

      // Add user message to the chat
      const userMessage: Message = {
        id: Date.now().toString(),
        content: message,
        role: 'user',
      };
  
      // Convert Message to LangChainChatMessage and add to chat
      const userLangChainMessage: LangChainChatMessage = {
        role: userMessage.role,
        content: userMessage.content
      };
      addMessage(userLangChainMessage);
      setIsLoading(true);
  
      // If deep research is enabled, log it
      if (deepResearch) {
        console.log('Deep research mode enabled');
      }
      
      // Create a simplified version of the chat history for the API
      const history = chatMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));
  
      // Select the appropriate API endpoint
      const endpoint = '/api/langchain-rag/chat';
  
      // Log the request parameters for debugging
      console.log(`Sending message with deepResearch=${deepResearch}`);
      console.log('Selected datasets:', selectedDatasets);
      console.log('Selected PDFs:', selectedPDFs);
      
      const requestBody = {
        message,
        history,
        deepResearch,
        maxIterations,
        model: selectedModel,
        searchStrategy,
        retrievalK,
        datasets: selectedDatasets,
        pdfs: selectedPDFs
      };
      
      console.log('Request body:', requestBody);
  
      // Make API request
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
  
      // Check if response is ok
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }
  
      // Parse response
      const data = await response.json();
  
      // Update source documents if available
      if (data.sourceDocuments) {
        setSourceDocuments(data.sourceDocuments);
      }
  
      // Create assistant message
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.content || data.response || 'No response received',
        role: 'assistant',
        sources: data.sourceDocuments || [],
      };
  
      // Convert to LangChainChatMessage and add to chat
      const assistantLangChainMessage: LangChainChatMessage = {
        role: assistantMessage.role,
        content: assistantMessage.content
      };
      addMessage(assistantLangChainMessage);

      return assistantMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Don't add error message to chat if it's a validation error (no datasets/PDFs selected)
      const errorMsg = error instanceof Error ? error.message : 'An unknown error occurred';
      
      if (!errorMsg.includes('Please select and embed at least one dataset or PDF')) {
        // Add error message to the chat for non-validation errors
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Error: ${errorMsg}`,
          role: 'assistant',
        };
    
        // Convert to LangChainChatMessage and add to chat
        const errorLangChainMessage: LangChainChatMessage = {
          role: errorMessage.role,
          content: errorMessage.content
        };
        addMessage(errorLangChainMessage);
      }
      
      setError(errorMsg);
      
      // Return a dummy error message for consistency
      return {
        id: (Date.now() + 1).toString(),
        content: `Error: ${errorMsg}`,
        role: 'assistant',
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Wrap the complex sendMessage function in a simpler interface
  const handleSendMessage = async (message: string): Promise<void> => {
    console.log('handleSendMessage called with:', {
      message,
      selectedDatasets,
      selectedPDFs,
      useDeepResearch
    });
    
    // Use the internal implementation which handles all the logic
    await _sendMessageInternal(message, useDeepResearch);
  };

  return {
    selectedDatasets,
    selectedPDFs,
    isLoading,
    isEmbedding,
    error,
    chatMessages,
    selectedModel,
    pineconeStatus,
    sourceDocuments,
    useDeepResearch,
    maxIterations,
    currentIterations,
    searchStrategy,
    retrievalK,
    setSelectedDatasets,
    setSelectedPDFs,
    setSelectedModel,
    setUseDeepResearch,
    setMaxIterations,
    setSearchStrategy,
    setRetrievalK,
    embedDataset,
    embedPDF,
    deleteEmbedding,
    addMessage,
    clearChat,
    sendMessage: handleSendMessage,
    checkPineconeStatus,
    fetchDatasets,
    fetchPDFs
  };
};

export default useLangChainRag;