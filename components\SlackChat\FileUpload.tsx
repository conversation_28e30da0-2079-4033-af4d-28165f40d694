"use client";

import { useState, useRef } from "react";
import { Upload, FileText, Image, FileSpreadsheet, Send, X, Paperclip } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useUploadThing } from "@/lib/uploadthing";
import { toast } from "sonner";

interface FileUploadProps {
  onFileSelect: (fileUrl: string, fileType: string) => void;
  onCancel: () => void;
  isUploading?: boolean;
  onMessageSubmit: (e: React.FormEvent, fileUrl?: string) => void;
  message: string;
  onMessageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

const UPLOADTHING_URL = "https://uploadthing.com/f/";

export function FileUpload({
  onFileSelect,
  onCancel,
  isUploading: externalIsUploading,
  onMessageSubmit,
  message,
  onMessageChange,
  placeholder
}: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  const { startUpload, isUploading } = useUploadThing("messageAttachment", {
    onClientUploadComplete: (res) => {
      if (res?.[0]) {
        // Pass both URL and file type
        onFileSelect(res[0].url, selectedFile?.type || "");
        toast.success("File uploaded successfully!");
        setSelectedFile(null);
        setPreview(null);
        setShowUpload(false);
      }
    },
    onUploadError: (error) => {
      toast.error(`Error uploading file: ${error.message}`);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedFile) {
        const result = await startUpload([selectedFile]);
        if (result?.[0]) {
          const fileUrl = `${UPLOADTHING_URL}${result[0].key}`;
          onMessageSubmit(e, fileUrl);
        }
      } else {
        onMessageSubmit(e);
      }
    } catch (error) {
      toast.error("Failed to send message");
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file: File) => {
    setSelectedFile(file);
    
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-8 w-8 text-blue-500" />;
    } else if (file.type === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-500" />;
    } else if (file.type === 'text/csv' || file.type.includes('spreadsheet')) {
      return <FileSpreadsheet className="h-8 w-8 text-green-500" />;
    }
    return <FileText className="h-8 w-8 text-muted-foreground" />;
  };

  return (
    <div className="flex flex-col">
      {/* Message Input with File Button */}
      <form onSubmit={handleSubmit} className="flex items-center gap-2 p-4 border-b">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="flex-shrink-0"
          onClick={() => setShowUpload(!showUpload)}
        >
          <Paperclip className="h-4 w-4" />
        </Button>
        <Input
          type="text"
          placeholder={placeholder || "Type a message..."}
          value={message}
          onChange={onMessageChange}
          className="flex-1"
        />
        <Button type="submit" size="sm" disabled={isUploading}>
          <Send className="h-4 w-4" />
        </Button>
      </form>

      {/* File Upload Area - Only shown when needed */}
      {showUpload && (
        <div className="p-4 border-b">
          {selectedFile ? (
            <div className="flex items-center gap-4 p-3 rounded-lg border bg-muted/50">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                {getFileIcon(selectedFile)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-foreground"
                onClick={() => {
                  setSelectedFile(null);
                  setPreview(null);
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div
              className={cn(
                "rounded-lg border-2 border-dashed p-4 transition-colors",
                dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center gap-2">
                <Upload className="h-6 w-6 text-muted-foreground" />
                <div className="text-center">
                  <p className="text-sm font-medium">Drop files here or click to upload</p>
                  <p className="text-xs text-muted-foreground">
                    Support: Images, PDF, CSV
                  </p>
                </div>
              </div>
            </div>
          )}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/*,.pdf,.csv"
            onChange={handleChange}
          />
        </div>
      )}
    </div>
  );
}