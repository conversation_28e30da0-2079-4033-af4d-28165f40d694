import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Brain, Sparkles, Zap, Stars, Cpu } from 'lucide-react';
import { cn } from '@/lib/utils';
import { LangChainModel } from '@/hooks/useLangChainRag';

interface ModelInfo {
  id: LangChainModel;
  name: string;
  description: string;
  icon: React.ElementType;
  color: string;
  provider: 'cohere' | 'together';
}

const MODELS: ModelInfo[] = [
  // Cohere models
  {
    id: 'command-r',
    name: 'Cohere Command-R',
    description: 'Fast and efficient for most tasks',
    icon: Brain,
    color: 'text-green-500',
    provider: 'cohere'
  },
  {
    id: 'command-r-plus',
    name: 'Cohere Command-R+',
    description: 'Enhanced reasoning and comprehension',
    icon: Stars,
    color: 'text-green-600',
    provider: 'cohere'
  },
  // Together AI models
  {
    id: 'llama-3-70b',
    name: '<PERSON>lam<PERSON> 3 (70B)',
    description: 'Meta\'s largest open model with strong reasoning',
    icon: Sparkles,
    color: 'text-blue-500',
    provider: 'together'
  },
  {
    id: 'llama-3-8b',
    name: 'Llama 3 (8B)',
    description: 'Efficient and fast open model',
    icon: Zap,
    color: 'text-blue-400',
    provider: 'together'
  },
  {
    id: 'mixtral-8x7b',
    name: 'Mixtral 8x7B',
    description: 'Powerful mixture-of-experts model',
    icon: Brain,
    color: 'text-purple-500',
    provider: 'together'
  },
  {
    id: 'mistral-7b',
    name: 'Mistral 7B',
    description: 'Efficient open model with good performance',
    icon: Cpu,
    color: 'text-indigo-500',
    provider: 'together'
  },
  {
    id: 'meta',
    name: 'Meta Llama 3.3 70B',
    description: 'Latest Meta Llama model with enhanced capabilities',
    icon: Stars,
    color: 'text-blue-600',
    provider: 'together'
  }
];

interface ModelSelectorProps {
  value: LangChainModel;
  onChange: (value: LangChainModel) => void;
  disabled?: boolean;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({ 
  value, 
  onChange,
  disabled = false
}) => {
  return (
    <Select 
      value={value} 
      onValueChange={(val) => onChange(val as LangChainModel)}
      disabled={disabled}
    >
      <SelectTrigger className="w-[250px]">
        <SelectValue placeholder="Select a model" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Cohere Models</SelectLabel>
          {MODELS.filter(m => m.provider === 'cohere').map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center gap-2">
                <model.icon className={cn("h-4 w-4", model.color)} />
                <span>{model.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
        <SelectGroup>
          <SelectLabel>Together AI Models</SelectLabel>
          {MODELS.filter(m => m.provider === 'together').map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center gap-2">
                <model.icon className={cn("h-4 w-4", model.color)} />
                <span>{model.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default ModelSelector;
